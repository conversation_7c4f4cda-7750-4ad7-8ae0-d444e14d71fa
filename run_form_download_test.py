#!/usr/bin/env python3
"""
运行Kaipoke Form Download测试
"""

import os
import sys
import yaml
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from logger_config import logger
from workflows.kaipoke_form_download import run

def main():
    """主函数"""
    logger.info("🚀 开始Kaipoke Form Download测试")
    
    # 检查环境变量
    required_vars = ['KAIPOKE_CORPORATION_ID', 'KAIPOKE_MEMBER_LOGIN_ID', 'KAIPOKE_PASSWORD']
    missing = [var for var in required_vars if not os.getenv(var)]
    
    if missing:
        logger.error(f"❌ 缺少环境变量: {missing}")
        return 1
    
    # 加载完整配置
    config_path = Path("configs/workflows.yaml")
    with open(config_path, 'r', encoding='utf-8') as f:
        full_config = yaml.safe_load(f)
    
    workflow_config = full_config.get('kaipoke_form_download', {})
    
    # 只测试第一个任务进行验证
    all_tasks = workflow_config.get('tasks', [])
    test_tasks = all_tasks[:1]  # 只取第一个任务进行测试
    
    test_config = {
        'config': workflow_config.get('config', {}),
        'tasks': test_tasks
    }
    
    # 设置测试参数
    test_config['config']['headless'] = False
    test_config['config']['download_path'] = "/tmp/kaipoke_form_test"
    
    logger.info(f"测试任务数量: {len(test_tasks)}")
    for task in test_tasks:
        logger.info(f"  - {task.get('task_id')}: {task.get('element_text')}")
    
    try:
        run(test_config)
        logger.info("✅ 测试完成")
        return 0
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}", exc_info=True)
        return 1

if __name__ == "__main__":
    sys.exit(main())
