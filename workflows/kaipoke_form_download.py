"""
Kaipoke Form Download Workflow (V8 - Configuration-Driven)
意图：作为由YAML文件驱动的执行引擎，完成37个账单预览文件的下载，并支持账号切换。

核心流程 (V8 - 配置驱动版):
1.  加载`workflows.yaml`和`selectors.yaml`中的配置。
2.  根据`workflows.yaml`中的任务定义，分离普通任务和特殊账号任务。
3.  登录主账号。
4.  按据点分组并处理普通任务：
    a. 导航到据点。
    b. 对每个任务，读取其`flow_pattern`和`params`。
    c. 调用与`flow_pattern`匹配的通用处理函数。
    d. 处理函数使用`params`中指定的选择器键，通过`SelectorExecutor`执行操作。
5.  所有普通任务完成后，登出。
6.  使用特殊任务中定义的凭证，登录第二个账号。
7.  处理特殊账号任务，逻辑同上。
"""

import asyncio
import os
from logger_config import logger
from core.browser.browser_manager import BrowserManager
import yaml
from core.rpa_tools.kaipoke_login_service import kaipoke_login_direct
from core.rpa_tools.kaipoke_common import get_previous_month_wareki
from core.popup_handler.kaipoke_popup_handler import handle_kaipoke_login_popups
from core.gsuite.drive_client import DriveClient

# --- Reusable Helper Functions ---

# --- Global Selectors Dictionary ---
SELECTORS = {}

def _load_selectors():
    """加载 selectors.yaml 文件"""
    global SELECTORS
    try:
        yaml_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'configs', 'selectors.yaml')
        with open(yaml_path, 'r', encoding='utf-8') as file:
            SELECTORS = yaml.safe_load(file)
        logger.info("✅ Selectors loaded successfully.")
    except Exception as e:
        logger.error(f"❌ Failed to load selectors.yaml: {e}")
        SELECTORS = {}

async def _navigate_to_form_page(page, nav_group_key: str):
    """根据配置的导航组密钥，导航到'様式出力'页面 - 优化版本"""
    logger.info(f"📋 导航到表单页面，使用导航组: '{nav_group_key}'")

    try:
        # 获取导航选择器配置
        if nav_group_key not in SELECTORS['kaipoke_form_download']['navigation']:
            raise KeyError(f"导航组 '{nav_group_key}' 在selectors.yaml中未找到")

        nav_selectors = SELECTORS['kaipoke_form_download']['navigation'][nav_group_key]
        hover_selector = nav_selectors['hover']
        click_selector = nav_selectors['click']

        logger.debug(f"悬停选择器: {hover_selector}")
        logger.debug(f"点击选择器: {click_selector}")

        # 步骤1: 悬停到菜单项
        logger.info("🖱️ 步骤1: 悬停到菜单项")
        hover_element = page.locator(hover_selector).first

        # 等待悬停元素出现并可见
        await hover_element.wait_for(state='visible', timeout=20000)
        await hover_element.hover(timeout=10000)
        logger.info("✅ 悬停成功")

        # 步骤2: 等待下拉菜单出现
        logger.info("⏳ 步骤2: 等待下拉菜单出现")
        await page.wait_for_timeout(2000)  # 等待下拉菜单动画完成

        # 步骤3: 点击様式出力菜单项
        logger.info("🖱️ 步骤3: 点击様式出力菜单项")
        click_element = page.locator(click_selector).first

        # 确保点击元素可见
        await click_element.wait_for(state='visible', timeout=10000)
        await click_element.click(timeout=10000)
        logger.info("✅ 点击様式出力成功")

        # 步骤4: 等待表单页面加载
        logger.info("⏳ 步骤4: 等待表单页面加载")
        await page.wait_for_load_state('domcontentloaded', timeout=30000)
        await page.wait_for_timeout(3000)  # 额外等待确保页面完全加载

        # 步骤5: 验证表单页面加载成功
        logger.info("✅ 步骤5: 验证表单页面")
        current_url = page.url
        logger.debug(f"当前页面URL: {current_url}")

        # 检查月份选择器作为页面加载成功的标志
        main_selector = SELECTORS['kaipoke_form_download']['actions']['month_selector_main'][0]
        alt_selector = SELECTORS['kaipoke_form_download']['actions']['month_selector_alt'][0]

        month_selector_found = False
        for selector in [main_selector, alt_selector]:
            try:
                await page.wait_for_selector(selector, state='visible', timeout=15000)
                logger.info(f"✅ 找到月份选择器: {selector}")
                month_selector_found = True
                break
            except:
                logger.debug(f"月份选择器未找到: {selector}")
                continue

        if not month_selector_found:
            logger.warning("⚠️ 未找到月份选择器，检查其他表单元素...")

            # 尝试查找其他表单元素作为备用验证
            form_indicators = [
                "form",
                ".box-input",
                ".box-refine",
                "table",
                "select"
            ]

            for indicator in form_indicators:
                try:
                    await page.wait_for_selector(indicator, state='visible', timeout=5000)
                    logger.info(f"✅ 找到表单元素: {indicator}")
                    break
                except:
                    continue

        # 等待网络空闲（可选）
        try:
            await page.wait_for_load_state('networkidle', timeout=10000)
        except Exception:
            logger.debug("⚠️ 网络未完全空闲，但继续执行")

        logger.info(f"✅ 成功导航到表单页面: {current_url}")
        return current_url

    except KeyError as e:
        logger.error(f"❌ 导航选择器配置错误: {e}")
        raise
    except Exception as e:
        logger.error(f"❌ 导航到表单页面失败 (nav_group: {nav_group_key}): {e}")
        # 保存错误截图
        try:
            await page.screenshot(path=f"/tmp/nav_error_{nav_group_key}.png")
            logger.info(f"导航错误截图已保存: /tmp/nav_error_{nav_group_key}.png")
        except:
            pass
        raise

async def _select_month(page):
    """选择上一个月的和历日期"""
    target_month = get_previous_month_wareki()
    logger.info(f"尝试选择月份: {target_month}")

    selectors_to_try = [
        SELECTORS['kaipoke_form_download']['actions']['month_selector_main'][0],
        SELECTORS['kaipoke_form_download']['actions']['month_selector_alt'][0]
    ]

    for i, selector in enumerate(selectors_to_try):
        try:
            logger.debug(f"尝试月份选择器 {i+1}: {selector}")

            # 确保选择器存在且可见
            await page.wait_for_selector(selector, state='visible', timeout=15000)

            # 尝试选择月份
            await page.select_option(selector, label=target_month, timeout=30000)
            logger.info(f"✅ 成功选择月份: {target_month} (选择器 {i+1})")
            return

        except Exception as e:
            logger.debug(f"月份选择器 {i+1} 失败: {e}")
            if i == len(selectors_to_try) - 1:  # 最后一个选择器也失败了
                logger.error(f"❌ 所有月份选择器都失败了")
                # 尝试手动输入或其他方法
                await _fallback_month_selection(page, target_month)
            continue

async def _fallback_month_selection(page, target_month: str):
    """月份选择的备用方法"""
    logger.warning("⚠️ 尝试备用月份选择方法")

    try:
        # 尝试查找所有select元素
        select_elements = await page.query_selector_all('select')

        for select in select_elements:
            try:
                # 检查是否包含月份选项
                options = await select.query_selector_all('option')
                for option in options:
                    option_text = await option.text_content()
                    if option_text and target_month in option_text:
                        await select.select_option(label=target_month)
                        logger.info(f"✅ 备用方法成功选择月份: {target_month}")
                        return
            except:
                continue

        logger.error(f"❌ 备用月份选择方法也失败了")
        raise Exception(f"无法选择月份: {target_month}")

    except Exception as e:
        logger.error(f"❌ 备用月份选择失败: {e}")
        raise

async def _perform_download(page, download_selector_key: str, final_filename: str, download_path: str):
    """执行文件下载"""
    logger.info(f"尝试下载文件，选择器键: {download_selector_key}")

    try:
        # 确保下载目录存在
        os.makedirs(download_path, exist_ok=True)

        download_selector = SELECTORS['kaipoke_form_download']['actions'][download_selector_key][0]
        logger.debug(f"使用下载选择器: {download_selector}")

        # 等待下载按钮出现并可见
        await page.wait_for_selector(download_selector, state='visible', timeout=60000)
        logger.debug("✅ 下载按钮已找到且可见")

        # 设置下载监听
        async with page.expect_download(timeout=120000) as download_info:
            # 点击下载按钮
            download_element = page.locator(download_selector).first
            await download_element.click(timeout=30000)
            logger.debug("✅ 下载按钮已点击")

            # 等待下载完成
            download = await download_info.value
            logger.debug(f"✅ 下载对象获取成功: {download.suggested_filename}")

            # 保存文件
            download_target_path = os.path.join(download_path, final_filename)
            await download.save_as(download_target_path)

            # 验证文件是否存在且大小合理
            if os.path.exists(download_target_path):
                file_size = os.path.getsize(download_target_path)
                if file_size > 0:
                    logger.info(f"✅ 文件下载成功: {download_target_path} (大小: {file_size} bytes)")
                    return download_target_path
                else:
                    logger.error(f"❌ 下载的文件为空: {download_target_path}")
                    return None
            else:
                logger.error(f"❌ 下载文件不存在: {download_target_path}")
                return None

    except Exception as e:
        logger.error(f"❌ 下载失败，选择器键 {download_selector_key}: {e}")

        # 尝试备用下载方法
        try:
            logger.warning("⚠️ 尝试备用下载方法")
            return await _fallback_download(page, download_selector_key, final_filename, download_path)
        except Exception as fallback_e:
            logger.error(f"❌ 备用下载方法也失败: {fallback_e}")
            raise e

async def _fallback_download(page, download_selector_key: str, final_filename: str, download_path: str):
    """备用下载方法"""
    logger.info(f"执行备用下载方法，原选择器键: {download_selector_key}")

    try:
        # 尝试查找所有可能的下载按钮
        download_patterns = [
            'button:has-text("ダウンロード")',
            'a:has-text("ダウンロード")',
            'input[value*="ダウンロード"]',
            'button:has-text("出力")',
            'a:has-text("出力")',
            '.btn:has-text("ダウンロード")',
            '.btn:has-text("出力")',
            'img[alt*="ダウンロード"]',
            'img[alt*="出力"]'
        ]

        for pattern in download_patterns:
            try:
                elements = page.locator(pattern)
                count = await elements.count()

                if count > 0:
                    logger.debug(f"找到备用下载按钮: {pattern}")

                    async with page.expect_download(timeout=60000) as download_info:
                        await elements.first.click(timeout=15000)
                        download = await download_info.value

                        download_target_path = os.path.join(download_path, final_filename)
                        await download.save_as(download_target_path)

                        if os.path.exists(download_target_path) and os.path.getsize(download_target_path) > 0:
                            logger.info(f"✅ 备用下载成功: {download_target_path}")
                            return download_target_path

            except Exception as e:
                logger.debug(f"备用下载模式失败 {pattern}: {e}")
                continue

        raise Exception("所有备用下载方法都失败")

    except Exception as e:
        logger.error(f"❌ 备用下载方法失败: {e}")
        raise

# --- Flow Pattern Handlers ---

async def _handle_simple_print_flow(page, params: dict, final_filename: str, download_path: str):
    """处理'点击印刷 -> 点击下载'的标准流程"""
    await _select_month(page)
    print_selector = SELECTORS['kaipoke_form_download']['actions'][params['print_selector']][0]
    await page.click(print_selector, timeout=30000)
    await page.wait_for_load_state('networkidle', timeout=60000)
    await page.wait_for_timeout(2000)
    return await _perform_download(page, params['download_selector'], final_filename, download_path)

async def _handle_copy_flow(page, params: dict, final_filename: str, download_path: str):
    """处理'点击复制 -> 选择年月 -> 点击印刷 -> 点击下载'的流程"""
    copy_selector = SELECTORS['kaipoke_form_download']['actions']['copy_button'][0]
    await page.click(copy_selector, timeout=30000)
    await page.wait_for_load_state('networkidle', timeout=60000)
    await _select_month(page)
    print_selector = SELECTORS['kaipoke_form_download']['actions'][params['print_selector']][0]
    await page.click(print_selector, timeout=30000)
    await page.wait_for_load_state('networkidle', timeout=60000)
    await page.wait_for_timeout(2000)
    return await _perform_download(page, "download_button_main", final_filename, download_path)

async def _handle_checkbox_flow(page, params: dict, final_filename: str, download_path: str):
    """处理包含复选框的流程"""
    await _select_month(page)
    prep_selector = SELECTORS['kaipoke_form_download']['actions'][params['prep_selector']][0]
    await page.click(prep_selector, timeout=30000)
    await page.wait_for_load_state('networkidle', timeout=60000)
    try:
        checkbox_already_output = SELECTORS['kaipoke_form_download']['actions']['checkbox_already_output'][0]
        await page.click(checkbox_already_output, timeout=10000)
    except Exception: pass
    try:
        checkbox_select_all = SELECTORS['kaipoke_form_download']['actions']['checkbox_select_all'][0]
        await page.click(checkbox_select_all, timeout=10000)
    except Exception: pass
    return await _perform_download(page, "download_link_preview", final_filename, download_path)

# --- Main Task Executor ---

async def execute_task(page, task_config: dict, download_path: str, skip_facility_nav: bool = False):
    """执行单个任务 - 修复版本"""
    task_id = task_config.get('task_id', 'N/A')
    final_filename = task_config.get('output_filename', f"download_{task_id}.pdf")
    flow_pattern = task_config.get('flow_pattern')
    params = task_config.get('params', {})
    element_text = task_config.get('element_text')

    logger.info(f"🚀 执行任务 {task_id}: {element_text} (流程: {flow_pattern})")

    try:
        current_url = page.url
        logger.info(f"📄 任务开始时页面URL: {current_url}")

        # 步骤1: 确保在正确的据点页面
        if not skip_facility_nav:
            logger.info(f"📍 步骤1: 导航到据点 {element_text}")
            if not await navigate_to_facility(page, element_text):
                raise Exception(f"导航到据点失败: {element_text}")
        else:
            logger.info(f"📍 步骤1: 跳过据点导航（已在正确据点）")
            # 验证当前是否在据点页面
            current_url = page.url
            if "COM020101.do" in current_url:
                logger.warning("⚠️ 当前仍在据点选择页面，需要重新导航到据点")
                if not await navigate_to_facility(page, element_text):
                    raise Exception(f"重新导航到据点失败: {element_text}")

        # 确认现在在据点页面
        current_url = page.url
        logger.info(f"📄 据点导航后页面URL: {current_url}")

        # 步骤2: 导航到表单页面（様式出力）
        logger.info(f"📋 步骤2: 导航到表单页面 (nav_group: {params.get('nav_group')})")
        navigation_success = False
        for attempt in range(3):
            try:
                await _navigate_to_form_page(page, params.get('nav_group'))
                navigation_success = True
                logger.info(f"✅ 表单页面导航成功 (尝试 {attempt + 1})")
                break
            except Exception as nav_e:
                logger.warning(f"⚠️ 表单页面导航尝试 {attempt + 1} 失败: {nav_e}")
                if attempt < 2:  # 不是最后一次尝试
                    await page.wait_for_timeout(3000)  # 等待3秒后重试
                    continue
                else:
                    raise nav_e

        if not navigation_success:
            raise Exception(f"导航到表单页面失败，任务 {task_id}")

        # 步骤3: 验证页面URL（放宽验证条件）
        current_url = page.url
        logger.info(f"📄 表单页面导航后URL: {current_url}")
        valid_patterns = ["MEM095001.do", "MEM090002.do", "bizhnc/exportList/", "MEM094501.do", "MEM094502.do"]
        url_valid = any(pattern in current_url for pattern in valid_patterns)

        if url_valid:
            logger.info(f"✅ 页面URL验证通过: {current_url}")
        else:
            logger.warning(f"⚠️ 页面URL可能不正确: {current_url}，但继续执行任务 {task_id}")

        # 步骤4: 处理弹窗
        await handle_kaipoke_login_popups(page, "form_download_template_based")

        # 步骤5: 执行任务流程
        logger.info(f"⚙️ 步骤5: 执行 {flow_pattern} 流程")
        handler_map = {
            "SIMPLE_PRINT": _handle_simple_print_flow,
            "COPY": _handle_copy_flow,
            "CHECKBOX": _handle_checkbox_flow,
        }

        handler = handler_map.get(flow_pattern)
        if not handler:
            raise ValueError(f"未知的流程模式 '{flow_pattern}' for task {task_id}")

        downloaded_file_path = await handler(page, params, final_filename, download_path)

        if downloaded_file_path:
            logger.info(f"✅ 任务 {task_id} 执行成功，文件: {downloaded_file_path}")
        else:
            logger.error(f"❌ 任务 {task_id} 执行失败，未生成下载文件")

        return downloaded_file_path

    except Exception as e:
        logger.error(f"❌ 任务 {task_id} 执行失败: {e}")
        # 保存错误截图
        try:
            screenshot_path = f"/tmp/task_{task_id}_error.png"
            await page.screenshot(path=screenshot_path)
            logger.info(f"错误截图已保存: {screenshot_path}")
        except:
            pass
        raise

# --- Core Workflow Orchestration ---

async def navigate_to_service_overview(page):
    """导航到服务概览页面（レセプト）- 修复版本"""
    logger.info("🏠 导航到服务概览页面 (レセプト)")

    try:
        # 检查当前页面状态
        current_url = page.url
        logger.info(f"📄 当前页面URL: {current_url}")

        # 如果已经在据点选择页面，直接返回
        if "COM020101.do" in current_url:
            logger.info("✅ 已在据点选择页面，无需导航")
            await handle_kaipoke_login_popups(page, "form_download_template_based")
            return

        # 如果在biztop页面，说明刚登录，需要点击レセプト菜单
        if "biztop" in current_url:
            logger.info("🔄 在主页面，需要点击レセプト菜单")

            # 尝试点击レセプト菜单
            receipt_clicked = False
            for attempt in range(3):
                try:
                    logger.info(f"📋 尝试点击レセプトメニュー (尝试 {attempt + 1}/3)")

                    # 等待页面完全加载
                    await page.wait_for_load_state('networkidle', timeout=30000)
                    await page.wait_for_timeout(2000)

                    # 点击レセプト菜单
                    await page.click(".mainCtg li:nth-of-type(1) a", timeout=15000)

                    # 等待导航完成
                    await page.wait_for_load_state('domcontentloaded', timeout=30000)
                    await page.wait_for_timeout(3000)

                    # 验证是否成功导航到据点选择页面
                    current_url = page.url
                    logger.info(f"📄 点击后URL: {current_url}")

                    if "COM020101.do" in current_url:
                        logger.info("✅ 成功导航到据点选择页面")
                        receipt_clicked = True
                        break
                    else:
                        logger.warning(f"⚠️ 点击后URL不正确: {current_url}")

                except Exception as e:
                    logger.warning(f"⚠️ レセプトメニュー点击尝试 {attempt + 1} 失败: {e}")
                    if attempt < 2:
                        await page.wait_for_timeout(3000)

            if not receipt_clicked:
                # 最后尝试：直接导航
                logger.info("🔄 最后尝试：直接导航到据点选择页面...")
                await page.goto("https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true",
                              wait_until='networkidle', timeout=30000)
                await page.wait_for_timeout(2000)
                current_url = page.url
                if "COM020101.do" in current_url:
                    logger.info("✅ 直接导航成功")
                else:
                    raise Exception(f"所有导航方法都失败，当前URL: {current_url}")

        # 如果在其他页面，直接导航到据点选择页面
        else:
            logger.info("🔄 在其他页面，直接导航到据点选择页面...")
            await page.goto("https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true",
                          wait_until='networkidle', timeout=30000)
            await page.wait_for_timeout(2000)
            current_url = page.url
            if "COM020101.do" in current_url:
                logger.info("✅ 直接导航成功")
            else:
                raise Exception(f"直接导航失败，当前URL: {current_url}")

        await handle_kaipoke_login_popups(page, "form_download_template_based")

    except Exception as e:
        logger.error(f"❌ 导航到服务概览页面失败: {e}")
        raise

async def navigate_to_facility(page, element_text: str):
    """导航到指定据点 - 修复版本"""
    logger.info(f"🏢 导航到据点: {element_text}")

    try:
        # 检查当前页面状态
        current_url = page.url
        logger.info(f"📄 当前页面URL: {current_url}")

        # 确保在据点选择页面
        if "COM020101.do" not in current_url:
            logger.info("🔄 不在据点选择页面，先导航到据点选择页面...")
            await navigate_to_service_overview(page)
            # 重新获取当前URL
            current_url = page.url
            logger.info(f"📄 导航后URL: {current_url}")

        # 确认在据点选择页面
        if "COM020101.do" not in current_url:
            raise Exception(f"无法导航到据点选择页面，当前URL: {current_url}")

        # 等待据点列表加载
        logger.info("⏳ 等待据点列表加载...")
        await page.wait_for_timeout(3000)

        # 查找据点链接
        logger.info(f"🔍 查找据点链接: {element_text}")

        # 尝试多种选择器策略
        selectors_to_try = [
            f'//a[contains(text(), "{element_text}")]',
            f'a:has-text("{element_text}")',
            f'ul li a:has-text("{element_text}")'
        ]

        facility_found = False
        for i, selector in enumerate(selectors_to_try):
            try:
                logger.info(f"🔍 尝试选择器 {i+1}: {selector}")

                # 等待元素出现
                await page.wait_for_selector(selector, timeout=10000)
                element = page.locator(selector).first

                if await element.is_visible():
                    logger.info(f"✅ 找到据点链接，准备点击")
                    await element.click(timeout=15000)
                    facility_found = True
                    break

            except Exception as e:
                logger.warning(f"⚠️ 选择器 {i+1} 失败: {e}")
                continue

        if not facility_found:
            logger.error(f"❌ 无法找到据点链接: {element_text}")
            # 保存截图用于调试
            try:
                await page.screenshot(path=f"/tmp/facility_not_found_{element_text.replace('/', '_')}.png")
                logger.info(f"据点未找到截图已保存")
            except:
                pass
            return False

        # 等待据点页面加载
        logger.info("⏳ 等待据点页面加载...")
        await page.wait_for_load_state('domcontentloaded', timeout=30000)
        await page.wait_for_timeout(3000)  # 额外等待确保页面完全加载

        # 验证据点页面加载成功
        current_url = page.url
        logger.info(f"📄 据点页面URL: {current_url}")

        # 检查据点页面的标志元素
        facility_indicators = [
            ".mainCtg",  # 主菜单
            "li:nth-of-type(6)",  # 导航菜单项
            ".dropdown",  # 下拉菜单
            "#jsddm"  # 菜单容器
        ]

        indicator_found = False
        for indicator in facility_indicators:
            try:
                await page.wait_for_selector(indicator, state='visible', timeout=5000)
                logger.info(f"✅ 据点页面标志确认: {indicator}")
                indicator_found = True
                break
            except:
                continue

        if not indicator_found:
            logger.warning("⚠️ 未找到明确的据点页面标志，但继续执行")

        # 处理弹窗
        await handle_kaipoke_login_popups(page, "form_download_template_based")

        logger.info(f"✅ 成功导航到据点: {element_text}")
        return True

    except Exception as e:
        logger.error(f"❌ 导航到据点失败 {element_text}: {e}")
        # 保存错误截图
        try:
            await page.screenshot(path=f"/tmp/facility_nav_error_{element_text.replace('/', '_')}.png")
            logger.info(f"据点导航错误截图已保存")
        except:
            pass
        return False

async def return_to_service_overview(page):
    """返回到服务概览页面 - 优化版本"""
    logger.info("🔙 返回到服务概览页面")

    try:
        current_url = page.url
        overview_url = "https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true"

        # 检查是否已经在目标页面
        if "COM020101" in current_url:
            logger.info("✅ 已在服务概览页面")
            return True

        logger.info(f"🔄 导航到概览页面: {overview_url}")

        # 使用更可靠的导航策略
        try:
            await page.goto(overview_url, wait_until='domcontentloaded', timeout=30000)
            await page.wait_for_timeout(2000)  # 等待页面稳定

            # 验证导航成功
            current_url = page.url
            if "COM020101" in current_url:
                logger.info("✅ 成功导航到服务概览页面")

                # 等待关键元素出现
                try:
                    await page.wait_for_selector("ul:nth-of-type(2)", state='visible', timeout=15000)
                    logger.debug("✅ 据点列表已加载")
                except:
                    logger.warning("⚠️ 据点列表加载超时，但继续执行")

                await handle_kaipoke_login_popups(page, "form_download_template_based")
                return True
            else:
                logger.error(f"❌ 导航后URL不正确: {current_url}")
                return False

        except Exception as nav_e:
            logger.error(f"❌ 页面导航失败: {nav_e}")
            return False

    except Exception as e:
        logger.error(f"❌ 返回服务概览页面失败: {e}")
        try:
            await page.screenshot(path="/tmp/error_return_to_overview.png")
            logger.info("错误截图已保存: /tmp/error_return_to_overview.png")
        except:
            pass
        return False

def group_tasks_by_facility(tasks: list):
    facility_groups = {}
    for task in tasks:
        element_text = task.get('element_text', '')
        if element_text not in facility_groups:
            facility_groups[element_text] = []
        facility_groups[element_text].append(task)
    return facility_groups

async def process_facility_group(page, facility_tasks: list, common_config: dict, drive_client: DriveClient):
    """处理同一据点的所有任务 - 修复版本"""
    element_text = facility_tasks[0].get('element_text', 'N/A')
    successful_tasks = 0

    logger.info(f"🏢 开始处理据点: {element_text} (任务数: {len(facility_tasks)})")

    # 处理该据点的所有任务
    for i, task_config in enumerate(facility_tasks):
        task_id = task_config.get('task_id', 'N/A')
        logger.info(f"⚙️ 处理任务 {i+1}/{len(facility_tasks)}: {task_id}")

        try:
            # 每个任务都独立处理导航，不跳过据点导航
            # 这样可以确保每个任务都从正确的状态开始
            downloaded_file_path = await execute_task(
                page, task_config, common_config['download_path'], skip_facility_nav=False
            )

            if downloaded_file_path:
                # 上传到Google Drive
                if common_config.get('gdrive_folder_id'):
                    try:
                        drive_client.upload_file(downloaded_file_path, common_config['gdrive_folder_id'])
                        logger.info(f"✅ 任务 {task_id} 完成并上传到Google Drive")
                    except Exception as upload_e:
                        logger.error(f"⚠️ 任务 {task_id} 下载成功但上传失败: {upload_e}")

                successful_tasks += 1
                logger.info(f"✅ 任务 {task_id} 完成")
            else:
                logger.error(f"❌ 任务 {task_id} 下载失败")

        except Exception as task_error:
            logger.error(f"❌ 任务 {task_id} 执行失败: {task_error}", exc_info=True)
            # 继续处理下一个任务
            continue

        # 任务间短暂等待
        if i < len(facility_tasks) - 1:  # 不是最后一个任务
            await asyncio.sleep(2)

    logger.info(f"✅ 据点 {element_text} 处理完成. 成功: {successful_tasks}/{len(facility_tasks)}")
    return successful_tasks

async def logout(page):
    logger.info("👋 Logging out...")
    try:
        logout_selector = SELECTORS['kaipoke']['login']['logout_button'][0]
        await page.click(logout_selector, timeout=30000)
        await page.wait_for_load_state('networkidle', timeout=60000)
        logger.info("✅ Logout successful.")
    except Exception as e:
        logger.error(f"❌ Logout failed: {e}")

async def async_run(config: dict):
    logger.info("🚀 Starting Kaipoke Form Download Workflow (V8 - Config-Driven)")
    common_config = config.get('config', {})
    tasks = config.get('tasks', [])
    
    browser_manager = BrowserManager()
    drive_client = DriveClient()
    total_successful = 0

    try:
        _load_selectors() # Load selectors at the beginning
        await browser_manager.start_browser(headless=common_config.get("headless", False))
        page = await browser_manager.get_page()

        # 分离普通任务和特殊账号任务
        normal_tasks = []
        special_account_tasks = []

        for task in tasks:
            # 检查任务是否指定了特殊账号环境变量
            if (task.get('corporation_id_env') or
                task.get('member_login_id_env') or
                task.get('password_env')):
                special_account_tasks.append(task)
            else:
                normal_tasks.append(task)

        logger.info(f"普通任务数量: {len(normal_tasks)}, 特殊账号任务数量: {len(special_account_tasks)}")

        if normal_tasks:
            logger.info("🔄 处理普通账号任务...")
            login_success = await kaipoke_login_direct(
                page,
                os.getenv(common_config.get('corporation_id_env')),
                os.getenv(common_config.get('member_login_id_env')),
                os.getenv(common_config.get('password_env')),
                common_config.get('login_url')
            )
            if login_success:
                logger.info("✅ 普通账号登录成功")

                # 登录后确保导航到据点选择页面
                await navigate_to_service_overview(page)

                facility_groups = group_tasks_by_facility(normal_tasks)
                logger.info(f"📊 普通账号任务分组: {len(facility_groups)} 个据点")

                for facility_name, facility_tasks in facility_groups.items():
                    logger.info(f"🏢 开始处理据点组: {facility_name}")
                    total_successful += await process_facility_group(page, facility_tasks, common_config, drive_client)
            else:
                logger.error("❌ 普通账号登录失败")

        if special_account_tasks:
            logger.info("🔄 处理特殊账号任务...")
            await logout(page)

            # 按账号分组特殊任务
            special_account_groups = {}
            for task in special_account_tasks:
                corp_id_env = task.get('corporation_id_env')
                member_id_env = task.get('member_login_id_env')
                password_env = task.get('password_env')

                # 创建账号标识
                account_key = f"{corp_id_env}_{member_id_env}_{password_env}"

                if account_key not in special_account_groups:
                    special_account_groups[account_key] = {
                        'corporation_id_env': corp_id_env,
                        'member_login_id_env': member_id_env,
                        'password_env': password_env,
                        'tasks': []
                    }
                special_account_groups[account_key]['tasks'].append(task)

            logger.info(f"📊 特殊账号分组: {len(special_account_groups)} 个账号")

            # 处理每个特殊账号
            for i, (account_key, account_info) in enumerate(special_account_groups.items()):
                logger.info(f"🔑 处理特殊账号 {i+1}/{len(special_account_groups)}: {account_key}")

                login_success = await kaipoke_login_direct(
                    page,
                    os.getenv(account_info.get('corporation_id_env')),
                    os.getenv(account_info.get('member_login_id_env')),
                    os.getenv(account_info.get('password_env')),
                    common_config.get('login_url')
                )

                if login_success:
                    logger.info(f"✅ 特殊账号登录成功: {account_key}")

                    # 登录后确保导航到据点选择页面
                    await navigate_to_service_overview(page)

                    facility_groups = group_tasks_by_facility(account_info['tasks'])
                    logger.info(f"📊 特殊账号任务分组: {len(facility_groups)} 个据点")

                    for facility_name, facility_tasks in facility_groups.items():
                        logger.info(f"🏢 开始处理据点组: {facility_name}")
                        total_successful += await process_facility_group(page, facility_tasks, common_config, drive_client)

                    # 如果还有其他特殊账号需要处理，则登出
                    if i < len(special_account_groups) - 1:
                        logger.info("🔄 准备切换到下一个特殊账号，先登出...")
                        await logout(page)
                else:
                    logger.error(f"❌ 特殊账号登录失败: {account_key}")

        logger.info(f"🎉 Workflow finished. Total successful tasks: {total_successful}/{len(tasks)}")

    finally:
        await browser_manager.close_browser()

def run(config: dict):
    asyncio.run(async_run(config))
