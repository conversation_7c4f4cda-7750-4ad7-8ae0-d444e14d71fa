"""
Kaipoke Tennki Workflow (修复版 - 智能浏览器管理 + 数据保护)
修复问题：
1. 浏览器实例管理问题：按数据量动态启动浏览器
2. 数据登录流程中的字段清空问题：增强数据保护机制
3. 数据分割策略：按利用者分割，确保测试数据正确分配
4. 失败数据日志：增强详细输出，便于手动登录

修复日期：2025-01-27
修复内容：
- 智能浏览器数量计算
- 数据保护机制增强
- 弹窗处理优化
- 表单状态管理改进
- 按利用者分割数据
- 增强失败数据日志输出
"""

import asyncio
import os
import sys
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from playwright.async_api import async_playwright, Browser, Page

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from logger_config import logger
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_with_env
from core.gsuite.sheets_client import SheetsClient
from core.rpa_tools.data_processor import DataProcessor
from core.rpa_tools.tennki_data_processor import TennkiDataProcessor
from core.rpa_tools.tennki_form_engine import TennkiFormEngine, TennkiFailedDataCollector
from core.rpa_tools.tennki_facility_manager import TennkiFacilityManager
from core.rpa_tools.tennki_performance_monitor import TennkiPerformanceMonitor
from core.popup_handler.popup_guardian import start_popup_guardian, stop_popup_guardian, stop_all_popup_guardians


class SmartBrowserManager:
    """🆕 智能浏览器管理器 - 根据数据量动态分配浏览器实例"""

    def __init__(self):
        self.playwright = None
        self.browsers = {}  # facility_name -> Browser
        self.pages = {}     # facility_name -> Page
        self.popup_guardians = {}  # facility_name -> guardian_status
        # 🆕 调整数据量阈值，适应测试环境
        self.data_volume_threshold = {
            'small': 50,     # 小数据量：1个浏览器 (适合测试环境)
            'medium': 150,   # 中等数据量：2个浏览器
            'large': 300     # 大数据量：3个浏览器
        }
        # 🆕 修复问题4：增强的浏览器同步协调机制
        self.login_lock = asyncio.Lock()
        self.login_queue = asyncio.Queue()
        self.login_status = {}  # facility_name -> login_status
        self.navigation_lock = asyncio.Lock()  # 🆕 导航互斥锁
        self.form_processing_lock = asyncio.Lock()  # 🆕 表单处理互斥锁
        self.browser_coordination = {}  # 🆕 浏览器协调状态

    async def initialize(self):
        """初始化Playwright"""
        if self.playwright is None:
            self.playwright = await async_playwright().start()
            logger.info("🚀 SmartBrowserManager初始化完成")

    def calculate_optimal_browser_count(self, total_records: int, total_users: int) -> int:
        """🔧 修复：根据分割数据启动多个浏览器"""
        logger.info(f"📊 计算最优浏览器数量: {total_records}条记录, {total_users}个用户")

        # 🔧 修复：根据用户数量启动多个浏览器，实现并行处理
        if total_users <= 1:
            optimal_count = 1
            reason = f"只有{total_users}个用户，使用单浏览器处理"
        elif total_users <= 3:
            optimal_count = total_users  # 每个用户一个浏览器
            reason = f"用户数量适中({total_users}个)，每个用户使用独立浏览器"
        else:
            # 用户数量较多时，限制最大浏览器数量避免资源过载
            optimal_count = min(4, total_users)  # 最多4个浏览器
            reason = f"用户数量较多({total_users}个)，使用{optimal_count}个浏览器并行处理"

        logger.info(f"✅ 最优浏览器数量: {optimal_count} ({reason})")
        logger.info(f"   - 多浏览器策略：根据用户数量启动多个浏览器实现并行处理")

        return optimal_count

    async def create_browser_for_facility(self, facility_name: str, headless: bool = False) -> tuple[Browser, Page]:
        """🔧 修复：简化浏览器创建逻辑，避免重复启动"""
        try:
            # 🔧 修复：检查浏览器是否已存在且有效
            if facility_name in self.browsers and facility_name in self.pages:
                try:
                    # 验证浏览器连接是否有效
                    browser = self.browsers[facility_name]
                    page = self.pages[facility_name]

                    if browser.is_connected() and not page.is_closed():
                        logger.info(f"🔄 据点 {facility_name} 的浏览器已存在且有效，复用中...")
                        return browser, page
                    else:
                        logger.warning(f"⚠️ 据点 {facility_name} 的浏览器连接无效，需要重新创建")
                        # 清理无效的浏览器实例
                        await self._cleanup_invalid_browser(facility_name)
                except Exception as e:
                    logger.warning(f"⚠️ 检查浏览器状态时出错: {e}，重新创建浏览器")
                    await self._cleanup_invalid_browser(facility_name)

            # 🔧 修复：简化创建逻辑，移除复杂的锁机制
            logger.info(f"🌐 为据点 {facility_name} 创建新的浏览器实例...")

            # 创建新的浏览器实例
            browser = await self.playwright.firefox.launch(headless=headless)
            page = await browser.new_page()

            # 存储实例
            self.browsers[facility_name] = browser
            self.pages[facility_name] = page
            self.login_status[facility_name] = 'created'

            # 🔧 修复：简化弹窗守护启动
            try:
                page_id = f"tennki_{facility_name}"
                guardian_started = await start_popup_guardian(page, page_id, "tennki_form_protected")
                self.popup_guardians[facility_name] = guardian_started

                if guardian_started:
                    logger.info(f"🛡️ 据点 {facility_name} 弹窗守护启动成功")
                else:
                    logger.warning(f"⚠️ 据点 {facility_name} 弹窗守护启动失败，继续处理")
            except Exception as e:
                logger.warning(f"⚠️ 弹窗守护启动失败: {e}，继续处理")
                self.popup_guardians[facility_name] = False

            # 等待浏览器初始化
            await asyncio.sleep(1)

            logger.info(f"✅ 据点 {facility_name} 的浏览器实例创建成功")
            return browser, page

        except Exception as e:
            logger.error(f"❌ 为据点 {facility_name} 创建浏览器失败: {e}")
            # 清理失败状态
            await self._cleanup_invalid_browser(facility_name)
            raise

    async def _cleanup_invalid_browser(self, facility_name: str):
        """🔧 清理无效的浏览器实例"""
        try:
            if facility_name in self.browsers:
                try:
                    await self.browsers[facility_name].close()
                except:
                    pass
                del self.browsers[facility_name]

            if facility_name in self.pages:
                del self.pages[facility_name]

            if facility_name in self.login_status:
                del self.login_status[facility_name]

            if facility_name in self.popup_guardians:
                del self.popup_guardians[facility_name]

        except Exception as e:
            logger.warning(f"⚠️ 清理无效浏览器时出错: {e}")

    async def _setup_enhanced_page_protection(self, page: Page, facility_name: str):
        """🆕 设置增强页面保护机制"""
        try:
            logger.debug(f"🛡️ 为据点 {facility_name} 设置增强页面保护...")

            # 注入增强保护脚本
            await page.evaluate("""
                () => {
                    // 🆕 数据保护全局标志
                    window.TENNKI_DATA_PROTECTION = {
                        enabled: true,
                        protectedFields: new Set(),
                        originalValues: new Map(),
                        facility: arguments[0]
                    };

                    // 🆕 保护表单数据不被意外清空
                    const protectFormData = () => {
                        const formFields = document.querySelectorAll('#registModal input, #registModal select, #registModal textarea');
                        formFields.forEach(field => {
                            if (field.value && field.value.trim() !== '') {
                                window.TENNKI_DATA_PROTECTION.protectedFields.add(field.id || field.name);
                                window.TENNKI_DATA_PROTECTION.originalValues.set(field.id || field.name, field.value);
                            }
                        });
                    };

                    // 🆕 监控字段值变化，防止意外清空
                    const monitorFieldChanges = () => {
                        const observer = new MutationObserver((mutations) => {
                            mutations.forEach((mutation) => {
                                if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                                    const field = mutation.target;
                                    const fieldId = field.id || field.name;
                                    
                                    if (window.TENNKI_DATA_PROTECTION.protectedFields.has(fieldId)) {
                                        const originalValue = window.TENNKI_DATA_PROTECTION.originalValues.get(fieldId);
                                        if (field.value === '' && originalValue) {
                                            console.warn('🛡️ 检测到字段被意外清空，恢复原值:', fieldId);
                                            field.value = originalValue;
                                        }
                                    }
                                }
                            });
                        });

                        const formContainer = document.querySelector('#registModal');
                        if (formContainer) {
                            observer.observe(formContainer, {
                                attributes: true,
                                subtree: true,
                                attributeFilter: ['value']
                            });
                        }
                    };

                    // 🆕 增强弹窗识别，避免误关闭数据表单
                    const enhancedPopupDetection = () => {
                        // 重写常见的弹窗关闭函数
                        const originalClose = window.close;
                        window.close = function() {
                            const registModal = document.querySelector('#registModal');
                            if (registModal && registModal.style.display !== 'none') {
                                console.log('🛡️ 阻止关闭窗口，保护数据登录表单');
                                return false;
                            }
                            return originalClose.apply(this, arguments);
                        };

                        // 监控模态框状态变化
                        const modalObserver = new MutationObserver((mutations) => {
                            mutations.forEach((mutation) => {
                                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                                    const modal = mutation.target;
                                    if (modal.id === 'registModal' && modal.style.display === 'none') {
                                        console.log('🛡️ 检测到数据表单被关闭，记录保护状态');
                                    }
                                }
                            });
                        });

                        const registModal = document.querySelector('#registModal');
                        if (registModal) {
                            modalObserver.observe(registModal, {
                                attributes: true,
                                attributeFilter: ['style']
                            });
                        }
                    };

                    // 初始化保护机制
                    protectFormData();
                    monitorFieldChanges();
                    enhancedPopupDetection();

                    console.log('🛡️ 增强页面保护机制已激活');
                }
            """, facility_name)

            logger.debug(f"✅ 据点 {facility_name} 增强页面保护设置完成")

        except Exception as e:
            logger.warning(f"⚠️ 设置据点 {facility_name} 页面保护失败: {e}")

    async def close_facility_browser(self, facility_name: str):
        """关闭指定据点的浏览器"""
        try:
            if facility_name in self.browsers:
                # 停止弹窗守护
                if facility_name in self.popup_guardians:
                    await stop_popup_guardian(facility_name)
                    del self.popup_guardians[facility_name]

                # 关闭浏览器
                await self.browsers[facility_name].close()
                del self.browsers[facility_name]
                del self.pages[facility_name]
                logger.info(f"✅ 据点 {facility_name} 的浏览器已关闭")
        except Exception as e:
            logger.error(f"❌ 关闭据点 {facility_name} 的浏览器失败: {e}")

    async def close_all_browsers(self):
        """关闭所有浏览器"""
        try:
            for facility_name in list(self.browsers.keys()):
                await self.close_facility_browser(facility_name)
            logger.info("✅ 所有浏览器已关闭")
        except Exception as e:
            logger.error(f"❌ 关闭所有浏览器失败: {e}")

    def get_active_browser_count(self) -> int:
        """获取活跃浏览器数量"""
        return len(self.browsers)


class EnhancedTennkiFacilityProcessor:
    """🆕 增强据点处理器 - 智能浏览器管理 + 按利用者分割"""

    def __init__(self, facility_config: dict, workflow_config: dict, smart_browser_manager: SmartBrowserManager):
        self.facility_config = facility_config
        self.workflow_config = workflow_config
        self.smart_browser_manager = smart_browser_manager
        self.facility_name = facility_config.get('name', 'unknown')
        self.facility_manager = None
        self.form_engine = None
        self.performance_monitor = None
        self.failed_data_collector = None

    async def initialize(self):
        """初始化处理器（不启动浏览器）"""
        try:
            logger.info(f"🏥 初始化据点处理器: {self.facility_name}")

            # 初始化智能浏览器管理器
            await self.smart_browser_manager.initialize()

            # 初始化性能监控器
            self.performance_monitor = TennkiPerformanceMonitor()

            # 初始化失败数据收集器
            self.failed_data_collector = TennkiFailedDataCollector()

            logger.info(f"✅ 据点处理器初始化完成: {self.facility_name}")

        except Exception as e:
            logger.error(f"❌ 据点处理器初始化失败: {e}")
            raise

    async def initialize_browser_and_login(self):
        """🔧 修复：初始化浏览器并登录，避免重复创建"""
        try:
            # 🔧 修复：检查是否已经初始化
            if hasattr(self, 'facility_manager') and self.facility_manager is not None:
                logger.info(f"🔄 据点 {self.facility_name} 浏览器已初始化，跳过重复创建")
                return

            logger.info(f"🌐 为据点 {self.facility_name} 初始化浏览器并登录")

            # 创建浏览器实例
            browser, page = await self.smart_browser_manager.create_browser_for_facility(
                self.facility_name,
                headless=self.workflow_config.get('headless', False)
            )

            selector_executor = SelectorExecutor(page)

            # 初始化据点管理器
            self.facility_manager = TennkiFacilityManager(selector_executor)

            self.form_engine = EnhancedTennkiFormEngine(
                selector_executor,
                self.performance_monitor,
                self.failed_data_collector
            )

            logger.info("🔐 开始登录系统...")
            # 登录系统
            await kaipoke_login_with_env(
                page,
                self.workflow_config.get('corporation_id_env', 'KAIPOKE_CORPORATION_ID'),
                self.workflow_config.get('login_id_env', 'KAIPOKE_MEMBER_LOGIN_ID'),
                self.workflow_config.get('password_env', 'KAIPOKE_PASSWORD'),
                self.workflow_config.get('login_url')
            )

            logger.info("📋 导航到レセプト菜单...")
            # 🔧 修复：导航到主菜单
            await self._navigate_to_receipt_menu(page)

            logger.info(f"✅ 据点 {self.facility_name} 浏览器初始化并登录完成")

        except Exception as e:
            logger.error(f"❌ 据点 {self.facility_name} 浏览器初始化失败: {e}")
            # 🔧 修复：清理失败状态
            self.facility_manager = None
            self.form_engine = None
            raise

    async def _navigate_to_receipt_menu(self, page):
        """🆕 导航到レセプト菜单（参考RPA代码实现）"""
        try:
            logger.info("📋 点击レセプト主菜单...")

            # 等待页面完全加载
            await page.wait_for_load_state('networkidle', timeout=30000)

            # 🆕 多重策略点击主菜单（参考RPA代码中的 .mainCtg li:nth-of-type(1) a）
            main_menu_clicked = False

            # 策略1：使用RPA代码中的精确选择器
            try:
                await page.wait_for_selector('.mainCtg li:nth-of-type(1) a', timeout=10000)
                await page.click('.mainCtg li:nth-of-type(1) a')
                main_menu_clicked = True
                logger.info("✅ 使用RPA精确选择器成功点击主菜单")
            except Exception as e:
                logger.debug(f"⚠️ RPA精确选择器失败: {e}")

            # 策略2：使用智能选择器
            if not main_menu_clicked:
                try:
                    success = await self.facility_manager.selector_executor.smart_click(
                        workflow="kaipoke_tennki",
                        category="navigation",
                        element="main_menu",
                        target_text="レセプト"
                    )
                    if success:
                        main_menu_clicked = True
                        logger.info("✅ 智能选择器成功点击主菜单")
                except Exception as e:
                    logger.debug(f"⚠️ 智能选择器失败: {e}")

            # 策略3：文本匹配
            if not main_menu_clicked:
                try:
                    await page.wait_for_selector("text=レセプト", timeout=10000)
                    await page.click("text=レセプト")
                    main_menu_clicked = True
                    logger.info("✅ 文本匹配成功点击主菜单")
                except Exception as e:
                    logger.debug(f"⚠️ 文本匹配失败: {e}")

            if not main_menu_clicked:
                raise Exception("所有策略都无法点击レセプト主菜单")

            # 等待页面加载
            await page.wait_for_load_state('networkidle', timeout=30000)
            logger.info("✅ レセプト主菜单点击完成，页面已加载")

        except Exception as e:
            logger.error(f"❌ 导航到レセプト菜单失败: {e}")
            raise

    async def _navigate_to_receipt_menu_for_browser(self, page, browser_id: int):
        """� 性能优化：快速レセプト菜单导航（减少延迟）"""
        try:
            logger.info(f"📋 浏览器 {browser_id} 点击レセプト主菜单...")

            # � 性能优化1：减少初始等待时间，只等待DOM加载完成
            await page.wait_for_load_state('domcontentloaded', timeout=10000)

            # � 性能优化2：先处理可能的弹窗，然后立即点击菜单
            await self._handle_post_login_popups(page, browser_id)

            main_menu_clicked = False

            # 策略1：使用RPA代码中的精确选择器
            try:
                await page.wait_for_selector('.mainCtg li:nth-of-type(1) a', timeout=5000)
                await page.click('.mainCtg li:nth-of-type(1) a')
                main_menu_clicked = True
                logger.info(f"✅ 浏览器 {browser_id} 使用RPA精确选择器成功点击主菜单")
            except Exception as e:
                logger.debug(f"⚠️ 浏览器 {browser_id} RPA精确选择器失败: {e}")

            # 策略2：文本匹配
            if not main_menu_clicked:
                try:
                    await page.wait_for_selector("text=レセプト", timeout=5000)
                    await page.click("text=レセプト")
                    main_menu_clicked = True
                    logger.info(f"✅ 浏览器 {browser_id} 文本匹配成功点击主菜单")
                except Exception as e:
                    logger.debug(f"⚠️ 浏览器 {browser_id} 文本匹配失败: {e}")

            if not main_menu_clicked:
                raise Exception(f"浏览器 {browser_id} 所有策略都无法点击レセプト主菜单")

            # � 性能优化3：减少页面加载等待时间，只等待必要的元素
            await page.wait_for_selector('.facility_list, .mainCtg', timeout=15000)
            logger.info(f"✅ 浏览器 {browser_id} レセプト主菜单点击完成，页面已加载")

        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_id} 导航到レセプト菜单失败: {e}")
            raise

    async def _handle_post_login_popups(self, page, browser_id: int):
        """🚀 快速处理登录后弹窗"""
        try:
            logger.debug(f"🔔 浏览器 {browser_id} 检查登录后弹窗...")

            # 快速检查常见弹窗并关闭
            popup_selectors = [
                '._icon-close__bF1y_',  # 通知弹窗关闭按钮
                '.modal .close',        # 模态框关闭按钮
                '.popup-close',         # 弹窗关闭按钮
                '[data-dismiss="modal"]' # Bootstrap模态框关闭
            ]

            for selector in popup_selectors:
                try:
                    popup_count = await page.locator(selector).count()
                    if popup_count > 0:
                        await page.click(selector)
                        logger.debug(f"✅ 浏览器 {browser_id} 关闭弹窗: {selector}")
                        await page.wait_for_timeout(500)  # 短暂等待弹窗关闭
                except Exception:
                    continue

        except Exception as e:
            logger.debug(f"⚠️ 浏览器 {browser_id} 弹窗处理失败: {e}")
            # 不抛出异常，继续执行

    async def _wait_for_page_ready(self, page, browser_id: int, page_type: str = "general"):
        """🆕 智能等待页面准备就绪，改善多浏览器同步问题"""
        try:
            logger.debug(f"⏳ 浏览器 {browser_id} 等待{page_type}页面准备就绪...")

            # 等待页面网络活动稳定
            await page.wait_for_load_state('networkidle', timeout=15000)

            # 根据页面类型等待特定元素
            if page_type == "login":
                await page.wait_for_selector('.mainCtg, .menu-item', timeout=8000)
            elif page_type == "receipt":
                await page.wait_for_selector('.facility_list, .mainCtg', timeout=8000)
            elif page_type == "facility":
                await page.wait_for_selector('h1, .service-header', timeout=8000)
            elif page_type == "nursing":
                # 🆕 增强月間スケージュール页面检测
                await page.wait_for_selector('.pulldownUser, h1:has-text("訪問看護"), #selectServiceOfferYm', timeout=10000)

            # 短暂等待确保页面完全稳定
            await page.wait_for_timeout(500)

            logger.debug(f"✅ 浏览器 {browser_id} {page_type}页面准备就绪")

        except Exception as e:
            logger.warning(f"⚠️ 浏览器 {browser_id} {page_type}页面等待失败: {e}")

    async def _verify_monthly_schedule_page_loaded(self, page, browser_id: int):
        """🆕 验证月間スケージュール页面是否正确加载"""
        try:
            logger.debug(f"🔍 浏览器 {browser_id} 验证月間スケージュール页面加载...")

            # 检查关键元素是否存在
            key_elements = [
                '.pulldownUser',  # 用户选择下拉框
                '#selectServiceOfferYm',  # 年月选择器
                'text=新規追加'  # 新增按钮
            ]

            found_elements = []
            for element in key_elements:
                try:
                    if await page.wait_for_selector(element, timeout=3000):
                        found_elements.append(element)
                except:
                    continue

            if len(found_elements) >= 2:  # 至少找到2个关键元素
                logger.info(f"✅ 浏览器 {browser_id} 月間スケージュール页面验证成功，找到元素: {found_elements}")
            else:
                logger.warning(f"⚠️ 浏览器 {browser_id} 月間スケージュール页面验证失败，只找到: {found_elements}")

                # 诊断当前页面状态
                current_url = page.url
                page_title = await page.title()
                logger.warning(f"🔍 浏览器 {browser_id} 当前页面 - URL: {current_url}, 标题: {page_title}")

        except Exception as e:
            logger.warning(f"⚠️ 浏览器 {browser_id} 月間スケージュール页面验证异常: {e}")

    async def _verify_facility_navigation_success(self, page, browser_id: int, facility_name: str):
        """🆕 验证据点导航是否成功"""
        try:
            logger.debug(f"🔍 浏览器 {browser_id} 验证据点导航成功...")

            # 检查当前URL
            current_url = page.url
            logger.debug(f"📄 浏览器 {browser_id} 据点导航后URL: {current_url}")

            # 检查页面标题
            try:
                page_title = await page.title()
                logger.debug(f"📋 浏览器 {browser_id} 页面标题: {page_title}")
            except:
                pass

            # 检查关键元素是否存在（据点页面特征）
            key_elements = [
                '.dropdown',  # 下拉菜单
                '.dropdown:nth-child(3)',  # 第3个下拉菜单（月間スケージュール所在）
                'h1',  # 页面标题
                '.service-header'  # 服务头部
            ]

            found_elements = []
            for element in key_elements:
                try:
                    if await page.wait_for_selector(element, timeout=3000):
                        found_elements.append(element)
                except:
                    continue

            if len(found_elements) >= 1:  # 至少找到1个关键元素
                logger.info(f"✅ 浏览器 {browser_id} 据点导航验证成功，找到元素: {found_elements}")
            else:
                logger.warning(f"⚠️ 浏览器 {browser_id} 据点导航验证失败，未找到预期元素")

                # 诊断当前页面状态
                await self._diagnose_current_page_state(page, browser_id)

        except Exception as e:
            logger.warning(f"⚠️ 浏览器 {browser_id} 据点导航验证异常: {e}")

    async def _diagnose_current_page_state(self, page, browser_id: int):
        """🆕 诊断当前页面状态"""
        try:
            logger.info(f"🔍 浏览器 {browser_id} 诊断当前页面状态...")

            # 获取页面基本信息
            current_url = page.url
            page_title = await page.title()

            logger.info(f"📄 浏览器 {browser_id} - URL: {current_url}")
            logger.info(f"📋 浏览器 {browser_id} - 标题: {page_title}")

            # 检查页面上的链接和按钮
            elements_info = await page.evaluate("""
                () => {
                    const elements = [];

                    // 获取所有链接
                    const links = Array.from(document.querySelectorAll('a')).slice(0, 5);
                    links.forEach(link => {
                        if (link.textContent.trim()) {
                            elements.push({
                                type: 'link',
                                text: link.textContent.trim(),
                                visible: link.offsetParent !== null
                            });
                        }
                    });

                    // 获取所有下拉菜单
                    const dropdowns = Array.from(document.querySelectorAll('.dropdown'));
                    dropdowns.forEach((dropdown, index) => {
                        elements.push({
                            type: 'dropdown',
                            text: `下拉菜单 ${index + 1}`,
                            visible: dropdown.offsetParent !== null
                        });
                    });

                    return elements;
                }
            """)

            logger.info(f"📋 浏览器 {browser_id} 页面元素:")
            for element in elements_info:
                visibility = "可见" if element['visible'] else "隐藏"
                logger.info(f"  - {element['type']}: {element['text']} ({visibility})")

        except Exception as e:
            logger.warning(f"⚠️ 浏览器 {browser_id} 页面状态诊断失败: {e}")

    async def process_facility_data(self):
        """🔧 修复：简化数据处理流程，避免浏览器重复启动"""
        try:
            logger.info(f"🔄 开始处理据点数据: {self.facility_name}")

            # 🔧 修复：获取和处理数据
            processed_data = await self._get_and_process_data()

            if not processed_data:
                logger.warning(f"⚠️ 据点 {self.facility_name} 没有数据需要处理")
                return

            logger.info(f"📊 据点 {self.facility_name} 共有 {len(processed_data)} 个用户的数据需要处理")

            # 🔧 修复：根据数据量决定处理策略
            total_records = sum(user['total_records'] for user in processed_data)
            total_users = len(processed_data)

            logger.info(f"📈 数据统计: {total_records} 条记录, {total_users} 个用户")

            # 🔧 修复：计算最优浏览器数量
            optimal_browser_count = self.smart_browser_manager.calculate_optimal_browser_count(total_records, total_users)

            if optimal_browser_count == 1:
                logger.info("🔧 使用单浏览器处理策略")
                await self._process_data_with_single_browser(processed_data)
            else:
                logger.info(f"🔧 使用多浏览器并行处理策略: {optimal_browser_count} 个浏览器")
                await self._process_data_with_multiple_browsers(processed_data, optimal_browser_count)

            logger.info(f"✅ 据点 {self.facility_name} 数据处理完成")

        except Exception as e:
            logger.error(f"❌ 据点 {self.facility_name} 数据处理失败: {e}")
            raise

    async def _get_and_process_data(self):
        """🆕 获取和处理数据（不启动浏览器）"""
        try:
            # 获取数据
            sheets_client = SheetsClient()
            spreadsheet_id = self.facility_config.get('spreadsheet_id')
            sheet_name = self.facility_config.get('sheet_name', '看護記録')
            
            logger.info(f"📊 从据点 {self.facility_name} 获取数据: {spreadsheet_id} / {sheet_name}")
            
            # 使用正确的API方法读取数据
            range_name = f"{sheet_name}!A2:AK"  # 从第2行开始读取，避免表头
            raw_data = sheets_client.read_sheet(spreadsheet_id, range_name)

            # 处理数据
            from core.rpa_tools.tennki_data_processor import TennkiDataProcessor
            
            # 使用TennkiDataProcessor处理数据
            data_processor = TennkiDataProcessor(sheets_client, sheet_name)
            # 设置spreadsheet_id
            data_processor.sheets_client.spreadsheet_id = spreadsheet_id
            processed_data = await data_processor.preprocess_all_data()

            return processed_data

        except Exception as e:
            logger.error(f"❌ 数据获取和处理失败: {e}")
            raise

    async def _process_data_with_smart_parallel_batches(self, processed_data: List[Dict], optimal_browser_count: int):
        """🆕 智能并行批次处理（按利用者分割）"""
        try:
            from core.rpa_tools.tennki_data_splitter import TennkiDataSplitter, create_batch_info_list

            # 🆕 按利用者分割：每个用户一个批次
            logger.info(f"📦 按利用者分割数据: {len(processed_data)} 个用户")
            
            # 创建按用户分割的批次
            user_batches = []
            for i, user_data in enumerate(processed_data):
                batch = [user_data]
                user_batches.append(batch)
                logger.info(f"  批次 {i+1}: 用户 {user_data.get('user_name', 'unknown')} ({user_data['total_records']} 条记录)")

            # 🆕 确保批次数量不超过最优浏览器数量
            if len(user_batches) > optimal_browser_count:
                logger.warning(f"⚠️ 用户数({len(user_batches)})超过浏览器数({optimal_browser_count})，进行合并...")
                user_batches = self._merge_excess_batches(user_batches, optimal_browser_count)

            # 创建批次信息
            batch_info_list = create_batch_info_list(user_batches)

            # 🆕 使用增强批次处理器
            from core.rpa_tools.tennki_batch_processor import TennkiBatchProcessor
            
            batch_processor = TennkiBatchProcessor(
                self.facility_config,
                self.workflow_config,
                self.smart_browser_manager
            )

            # 🆕 设置动态并发数量
            batch_processor.max_concurrent_batches = optimal_browser_count
            batch_processor.semaphore = asyncio.Semaphore(optimal_browser_count)

            success = await batch_processor.process_batches_parallel(batch_info_list)

            # 合并失败数据
            batch_failed_collector = batch_processor.get_failed_data_collector()
            self.failed_data_collector.failed_records.extend(batch_failed_collector.failed_records)
            self.failed_data_collector.failed_users.extend(batch_failed_collector.failed_users)

            if not success:
                logger.warning("⚠️ 部分批次处理失败")

        except Exception as e:
            logger.error(f"❌ 智能并行批次处理失败: {e}", exc_info=True)
            raise

    def _merge_excess_batches(self, split_batches: List[List[Dict]], target_count: int) -> List[List[Dict]]:
        """🆕 合并多余的批次"""
        if len(split_batches) <= target_count:
            return split_batches

        logger.info(f"🔄 合并批次: {len(split_batches)} → {target_count}")

        # 简单合并策略：将最小的批次合并到其他批次中
        while len(split_batches) > target_count:
            # 找到最小的批次
            smallest_idx = min(range(len(split_batches)), 
                             key=lambda i: sum(user['total_records'] for user in split_batches[i]))
            
            # 找到第二小的批次进行合并
            remaining_batches = [i for i in range(len(split_batches)) if i != smallest_idx]
            target_idx = min(remaining_batches, 
                           key=lambda i: sum(user['total_records'] for user in split_batches[i]))

            # 合并批次
            split_batches[target_idx].extend(split_batches[smallest_idx])
            split_batches.pop(smallest_idx)

        logger.info(f"✅ 批次合并完成: {len(split_batches)} 个批次")
        return split_batches

    async def _process_data_with_single_browser(self, processed_data: List[Dict]):
        """🔧 修复：单浏览器稳定处理，避免重复启动"""
        try:
            logger.info(f"🌐 开始单浏览器处理 - 据点: {self.facility_name}")

            # 🔧 修复：确保浏览器只启动一次
            await self.initialize_browser_and_login()

            # 🔧 修复：按照正确的流程顺序导航
            facility_name = self.facility_config.get('name')
            element_text = self.facility_config.get('element_text')

            logger.info(f"📍 导航到据点: {facility_name}")
            # 1. 导航到据点
            await self.facility_manager.navigate_to_facility(element_text, facility_name)

            logger.info("🏥 导航到月間スケージュール页面")
            # 2. 导航到月間スケージュール页面
            await self.facility_manager.navigate_to_nursing_page()

            # 🆕 验证月間スケージュール页面是否正确加载
            page = self.smart_browser_manager.pages[self.facility_name]
            await self._verify_monthly_schedule_page_loaded(page, 1)

            logger.info(f"📝 开始处理 {len(processed_data)} 个用户的数据")
            # 3. 处理数据 - 使用修复后的表单引擎
            await self.form_engine.process_batch_data_sequential(processed_data, self.facility_config)

            logger.info(f"✅ 据点 {self.facility_name} 单浏览器处理完成")

        except Exception as e:
            logger.error(f"❌ 单浏览器数据处理失败: {e}", exc_info=True)
            raise

    async def _process_data_with_multiple_browsers(self, processed_data: List[Dict], browser_count: int):
        """🆕 多浏览器并行处理数据"""
        all_failed_records = []

        try:
            logger.info(f"🌐 开始多浏览器并行处理 - 据点: {self.facility_name}, 浏览器数量: {browser_count}")

            # 🆕 按用户分割数据
            user_batches = self._split_data_by_users(processed_data, browser_count)

            # 🆕 为每个批次创建独立的浏览器任务
            tasks = []
            for i, batch in enumerate(user_batches):
                task = self._process_batch_with_dedicated_browser(batch, i + 1)
                tasks.append(task)

            # 🆕 并行执行所有任务
            logger.info(f"🚀 启动 {len(tasks)} 个并行浏览器任务")
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 🆕 检查结果并收集失败记录
            success_count = 0
            total_failed_records = 0

            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"❌ 浏览器任务 {i+1} 异常失败: {result}")
                    # 如果任务异常失败，将该批次的所有数据标记为失败
                    batch = user_batches[i]
                    for user_data in batch:
                        for insurance_type, records in user_data.get('insurance_groups', {}).items():
                            for record in records:
                                failed_record = {
                                    'browser_id': i + 1,
                                    'error': str(result),
                                    'error_type': 'task_exception',
                                    'user_name': user_data.get('user_name', '未知用户'),
                                    'insurance_type': insurance_type,
                                    'record_data': record,
                                    'timestamp': datetime.now().isoformat()
                                }
                                all_failed_records.append(failed_record)
                elif isinstance(result, list):
                    # 正常返回的失败记录列表
                    success_count += 1
                    failed_count = len(result)
                    total_failed_records += failed_count
                    all_failed_records.extend(result)
                    logger.info(f"✅ 浏览器任务 {i+1} 完成，失败记录: {failed_count} 条")
                else:
                    success_count += 1
                    logger.info(f"✅ 浏览器任务 {i+1} 完成，无失败记录")

            logger.info(f"📊 多浏览器处理完成: {success_count}/{len(tasks)} 个任务成功")
            logger.info(f"📊 总失败记录数: {len(all_failed_records)} 条")

            # 🆕 将失败记录添加到全局失败数据收集器
            if all_failed_records:
                for failed_record in all_failed_records:
                    # 构造符合TennkiFailedDataCollector格式的记录
                    record_dict = {
                        'user_name': failed_record['user_name'],
                        'insurance_type': failed_record['insurance_type'],
                        'raw_data': failed_record['record_data'],
                        'browser_id': failed_record['browser_id'],
                        'error_type': failed_record['error_type']
                    }

                    self.failed_data_collector.record_failed_record(
                        record_dict,
                        failed_record['error'],
                        self.facility_name
                    )

        except Exception as e:
            logger.error(f"❌ 多浏览器数据处理失败: {e}", exc_info=True)
            raise

    def _split_data_by_users(self, processed_data: List[Dict], browser_count: int) -> List[List[Dict]]:
        """按用户分割数据到多个批次"""
        if browser_count >= len(processed_data):
            # 每个用户一个批次
            return [[user] for user in processed_data]

        # 平均分配用户到各个批次
        batch_size = len(processed_data) // browser_count
        remainder = len(processed_data) % browser_count

        batches = []
        start_idx = 0

        for i in range(browser_count):
            # 前remainder个批次多分配一个用户
            current_batch_size = batch_size + (1 if i < remainder else 0)
            end_idx = start_idx + current_batch_size

            batch = processed_data[start_idx:end_idx]
            batches.append(batch)

            start_idx = end_idx

            logger.info(f"📦 批次 {i+1}: {len(batch)} 个用户")

        return batches

    async def _process_batch_with_dedicated_browser(self, user_batch: List[Dict], browser_id: int):
        """使用专用浏览器处理用户批次"""
        browser_facility_name = f"{self.facility_name}_browser_{browser_id}"
        failed_records = []

        try:
            logger.info(f"🌐 浏览器 {browser_id} 开始处理 {len(user_batch)} 个用户")

            # 🆕 为这个批次创建专用浏览器
            browser, page = await self.smart_browser_manager.create_browser_for_facility(
                browser_facility_name,
                headless=self.workflow_config.get('headless', False)
            )

            # 🆕 创建专用的选择器执行器和表单引擎
            selector_executor = SelectorExecutor(page)
            facility_manager = TennkiFacilityManager(selector_executor)
            form_engine = EnhancedTennkiFormEngine(
                selector_executor,
                self.performance_monitor,
                self.failed_data_collector
            )

            # 🚀 同步优化：统一登录重试机制，减少浏览器间差异
            max_login_retries = 2
            login_success = False

            for attempt in range(max_login_retries):
                try:
                    logger.info(f"🔐 浏览器 {browser_id} 开始登录系统 (尝试 {attempt + 1}/{max_login_retries})...")

                    # 🚀 优化：减少随机延迟，提高同步效率
                    import random
                    sync_delay = random.uniform(0.2, 0.8)  # 🆕 减少延迟范围：0.2-0.8秒
                    await page.wait_for_timeout(int(sync_delay * 1000))

                    await kaipoke_login_with_env(
                        page,
                        self.workflow_config.get('corporation_id_env', 'KAIPOKE_CORPORATION_ID'),
                        self.workflow_config.get('login_id_env', 'KAIPOKE_MEMBER_LOGIN_ID'),
                        self.workflow_config.get('password_env', 'KAIPOKE_PASSWORD'),
                        self.workflow_config.get('login_url')
                    )

                    # 🆕 登录成功后智能等待页面准备就绪
                    await self._wait_for_page_ready(page, browser_id, "login")

                    login_success = True
                    logger.info(f"✅ 浏览器 {browser_id} 登录成功")
                    break
                except Exception as e:
                    logger.warning(f"⚠️ 浏览器 {browser_id} 登录失败 (尝试 {attempt + 1}): {e}")
                    if attempt < max_login_retries - 1:
                        # 🚀 优化：进一步减少重试等待时间
                        retry_delay = random.uniform(1.0, 2.0)  # 🆕 从2-4秒减少到1-2秒
                        await page.wait_for_timeout(int(retry_delay * 1000))

            if not login_success:
                raise Exception(f"浏览器 {browser_id} 登录失败，已尝试 {max_login_retries} 次")

            # 🚀 同步优化：统一レセプト菜单导航，减少浏览器间差异
            max_nav_retries = 2
            nav_success = False

            for attempt in range(max_nav_retries):
                try:
                    logger.info(f"📋 浏览器 {browser_id} 导航到レセプト菜单 (尝试 {attempt + 1}/{max_nav_retries})...")

                    # 🚀 优化：减少随机延迟，提高导航效率
                    sync_delay = random.uniform(0.1, 0.5)  # 🆕 减少延迟范围：0.1-0.5秒
                    await page.wait_for_timeout(int(sync_delay * 1000))

                    await self._navigate_to_receipt_menu_for_browser(page, browser_id)

                    # 🆕 レセプト菜单导航后智能等待页面准备就绪
                    await self._wait_for_page_ready(page, browser_id, "receipt")

                    nav_success = True
                    logger.info(f"✅ 浏览器 {browser_id} レセプト菜单导航成功")
                    break
                except Exception as e:
                    logger.warning(f"⚠️ 浏览器 {browser_id} レセプト菜单导航失败 (尝试 {attempt + 1}): {e}")
                    if attempt < max_nav_retries - 1:
                        # 🚀 同步优化：减少重试等待时间
                        retry_delay = random.uniform(2.0, 3.0)  # 2-3秒随机延迟
                        await page.wait_for_timeout(int(retry_delay * 1000))

            if not nav_success:
                raise Exception(f"浏览器 {browser_id} レセプト菜单导航失败，已尝试 {max_nav_retries} 次")

            # � 修复问题3：增强据点导航同步机制
            facility_name = self.facility_config.get('name')
            element_text = self.facility_config.get('element_text')

            logger.info(f"📍 浏览器 {browser_id} 导航到据点: {facility_name}")

            # � 修复问题3：增强据点导航重试机制
            max_facility_retries = 3
            facility_success = False

            for attempt in range(max_facility_retries):
                try:
                    logger.info(f"🏥 浏览器 {browser_id} 尝试据点导航 (尝试 {attempt + 1}/{max_facility_retries})")

                    # 🚀 优化：增加同步延迟，避免浏览器间冲突
                    sync_delay = random.uniform(0.5, 1.5)  # 🆕 增加延迟范围：0.5-1.5秒
                    await page.wait_for_timeout(int(sync_delay * 1000))

                    # 🆕 在导航前检查页面状态
                    current_url = page.url
                    logger.debug(f"🌐 浏览器 {browser_id} 导航前URL: {current_url}")

                    await facility_manager.navigate_to_facility(element_text, facility_name)

                    # 🆕 据点导航后智能等待页面准备就绪
                    await self._wait_for_page_ready(page, browser_id, "facility")

                    # 🆕 额外验证：检查是否真正进入了据点页面
                    await self._verify_facility_navigation_success(page, browser_id, facility_name)

                    facility_success = True
                    logger.info(f"✅ 浏览器 {browser_id} 据点导航成功")
                    break

                except Exception as e:
                    error_msg = str(e)
                    logger.warning(f"⚠️ 浏览器 {browser_id} 据点导航失败 (尝试 {attempt + 1}): {error_msg}")

                    # 🆕 根据错误类型调整重试策略
                    if "Timeout" in error_msg and attempt < max_facility_retries - 1:
                        # 超时错误：增加重试延迟
                        retry_delay = random.uniform(3.0, 5.0)  # 🆕 超时时增加延迟：3-5秒
                        logger.info(f"⏳ 浏览器 {browser_id} 检测到超时，等待 {retry_delay:.1f}秒 后重试...")
                        await page.wait_for_timeout(int(retry_delay * 1000))
                    elif attempt < max_facility_retries - 1:
                        # 其他错误：标准重试延迟
                        retry_delay = random.uniform(2.0, 3.0)  # 🆕 标准延迟：2-3秒
                        logger.info(f"⏳ 浏览器 {browser_id} 等待 {retry_delay:.1f}秒 后重试...")
                        await page.wait_for_timeout(int(retry_delay * 1000))

            if not facility_success:
                raise Exception(f"浏览器 {browser_id} 据点导航失败，已尝试 {max_facility_retries} 次")

            # � 修复问题3：增强月間スケージュール页面导航同步机制
            logger.info(f"🏥 浏览器 {browser_id} 导航到月間スケージュール页面")

            max_nursing_retries = 3
            nursing_success = False

            for attempt in range(max_nursing_retries):
                try:
                    logger.info(f"🎯 浏览器 {browser_id} 尝试导航到月間スケージュール (尝试 {attempt + 1}/{max_nursing_retries})")

                    # 🚀 优化：增加同步延迟，确保页面完全加载
                    sync_delay = random.uniform(0.5, 1.2)  # 🆕 增加延迟范围：0.5-1.2秒
                    await page.wait_for_timeout(int(sync_delay * 1000))

                    # 🆕 在导航前检查页面状态
                    current_url = page.url
                    logger.debug(f"🌐 浏览器 {browser_id} 当前URL: {current_url}")

                    await facility_manager.navigate_to_nursing_page()

                    # 🆕 月間スケージュール页面导航后智能等待页面准备就绪
                    await self._wait_for_page_ready(page, browser_id, "nursing")

                    # 🆕 额外验证：检查是否真正进入了月間スケージュール页面
                    await self._verify_monthly_schedule_page_loaded(page, browser_id)

                    nursing_success = True
                    logger.info(f"✅ 浏览器 {browser_id} 月間スケージュール页面导航成功")
                    break
                except Exception as e:
                    logger.warning(f"⚠️ 浏览器 {browser_id} 月間スケージュール页面导航失败 (尝试 {attempt + 1}): {e}")
                    if attempt < max_nursing_retries - 1:
                        # 🚀 优化：增加重试延迟时间，给页面更多时间稳定
                        retry_delay = random.uniform(2.0, 3.5)  # 🆕 增加延迟：2-3.5秒
                        logger.info(f"⏳ 浏览器 {browser_id} 等待 {retry_delay:.1f}秒 后重试...")
                        await page.wait_for_timeout(int(retry_delay * 1000))

            if not nursing_success:
                raise Exception(f"浏览器 {browser_id} 月間スケージュール页面导航失败，已尝试 {max_nursing_retries} 次")

            # 🆕 处理用户批次数据 - 记录失败的数据
            logger.info(f"📝 浏览器 {browser_id} 开始处理用户数据")
            batch_failed_records = await form_engine.process_batch_data_sequential(user_batch, self.facility_config)

            # 收集失败记录
            if batch_failed_records:
                failed_records.extend(batch_failed_records)
                logger.warning(f"⚠️ 浏览器 {browser_id} 有 {len(batch_failed_records)} 条记录处理失败")

            logger.info(f"✅ 浏览器 {browser_id} 处理完成")
            return failed_records

        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_id} 处理失败: {e}")

            # 🆕 如果整个浏览器处理失败，将所有数据标记为失败
            for user_data in user_batch:
                for insurance_type, records in user_data.get('insurance_groups', {}).items():
                    for record in records:
                        failed_record = {
                            'browser_id': browser_id,
                            'error': str(e),
                            'error_type': 'browser_failure',
                            'user_name': user_data.get('user_name', '未知用户'),
                            'insurance_type': insurance_type,
                            'record_data': record,
                            'timestamp': datetime.now().isoformat()
                        }
                        failed_records.append(failed_record)

            return failed_records

        finally:
            # 🆕 清理专用浏览器
            try:
                await self.smart_browser_manager.close_facility_browser(browser_facility_name)
                logger.info(f"🔒 浏览器 {browser_id} 清理完成")
            except Exception as e:
                logger.warning(f"⚠️ 浏览器 {browser_id} 清理失败: {e}")

    async def cleanup(self):
        """清理资源"""
        try:
            await self.smart_browser_manager.close_facility_browser(self.facility_name)
            logger.info(f"🔒 据点 {self.facility_name} 资源清理完成")
        except Exception as e:
            logger.error(f"❌ 据点 {self.facility_name} 资源清理失败: {e}")


class EnhancedTennkiFormEngine(TennkiFormEngine):
    """🆕 增强表单引擎 - 数据保护机制"""

    def __init__(self, selector_executor: SelectorExecutor, performance_monitor, failed_data_collector=None):
        super().__init__(selector_executor, performance_monitor, failed_data_collector)
        self.data_protection_enabled = True
        self.protected_field_values = {}

    async def _process_single_record(self, record: Dict, insurance_type: str):
        """处理单条记录（参考RPA代码实现）"""
        row_index = record['row_index']
        row = record.get('raw_data', [])
        
        try:
            logger.debug(f"📝 处理记录 (行 {row_index}) - 参考RPA代码实现")
            
            # 🆕 启用数据保护
            await self._enable_data_protection()
            
            # 🆕 根据参考文件实现保险种别处理
            if insurance_type == "介護":
                await self._process_kaigo_insurance_reference(row)
            elif insurance_type == "医療":
                await self._process_iryou_insurance_reference(row)
            elif insurance_type == "精神医療":
                await self._process_seishin_iryou_insurance_reference(row)
            else:
                logger.warning(f"⚠️ 未知保险类型: {insurance_type}")
                await super()._process_single_record(record, insurance_type)
            
            # 🆕 验证数据完整性
            await self._verify_data_integrity()
            
        except Exception as e:
            logger.error(f"❌ 记录 (行 {row_index}) 处理失败: {e}")
            raise
        finally:
            # 🆕 清理保护状态
            await self._cleanup_data_protection()

    async def _enable_data_protection(self):
        """🆕 启用数据保护机制"""
        if not self.data_protection_enabled:
            return
            
        try:
            page = self.selector_executor.page
            
            # 保存当前表单数据
            current_values = await page.evaluate("""
                () => {
                    const values = {};
                    const fields = document.querySelectorAll('#registModal input, #registModal select, #registModal textarea');
                    fields.forEach(field => {
                        if (field.value && field.value.trim() !== '') {
                            values[field.id || field.name || field.tagName + '_' + Array.from(field.parentNode.children).indexOf(field)] = field.value;
                        }
                    });
                    return values;
                }
            """)
            
            self.protected_field_values = current_values
            logger.debug(f"🛡️ 数据保护已启用，保护 {len(current_values)} 个字段")
            
        except Exception as e:
            logger.warning(f"⚠️ 启用数据保护失败: {e}")

    async def _verify_data_integrity(self):
        """🆕 验证数据完整性"""
        if not self.data_protection_enabled or not self.protected_field_values:
            return
            
        try:
            page = self.selector_executor.page
            
            # 检查字段是否被意外清空
            current_values = await page.evaluate("""
                () => {
                    const values = {};
                    const fields = document.querySelectorAll('#registModal input, #registModal select, #registModal textarea');
                    fields.forEach(field => {
                        values[field.id || field.name || field.tagName + '_' + Array.from(field.parentNode.children).indexOf(field)] = field.value || '';
                    });
                    return values;
                }
            """)
            
            # 检测被清空的字段
            cleared_fields = []
            for field_id, original_value in self.protected_field_values.items():
                current_value = current_values.get(field_id, '')
                if original_value and not current_value:
                    cleared_fields.append(field_id)
            
            if cleared_fields:
                logger.warning(f"⚠️ 检测到 {len(cleared_fields)} 个字段被意外清空: {cleared_fields}")
                
                # 尝试恢复被清空的字段
                for field_id in cleared_fields:
                    original_value = self.protected_field_values[field_id]
                    await page.evaluate(f"""
                        () => {{
                            const field = document.querySelector('[id="{field_id}"], [name="{field_id}"]');
                            if (field) {{
                                field.value = '{original_value}';
                                field.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                field.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                console.log('🛡️ 恢复字段值:', '{field_id}', '{original_value}');
                            }}
                        }}
                    """)
                
                logger.info(f"✅ 已尝试恢复 {len(cleared_fields)} 个被清空的字段")
            else:
                logger.debug("✅ 数据完整性验证通过，无字段被清空")
                
        except Exception as e:
            logger.warning(f"⚠️ 数据完整性验证失败: {e}")

    async def _cleanup_data_protection(self):
        """🆕 清理数据保护状态"""
        self.protected_field_values.clear()

    async def _process_kaigo_insurance_reference(self, row: List):
        """🆕 参考RPA代码处理介護保险（完全按照参考文件顺序）"""
        page = self.selector_executor.page
        
        try:
            logger.info("🏥 参考RPA代码处理介護保险")
            
            # 1. 点击介護保险按钮
            await page.click('#inPopupInsuranceDivision01')
            await page.wait_for_timeout(3000)
            
            # 2. 根据row[26]判断是否为介護予防
            if len(row) > 26 and row[26] == "":
                # 标准介護保险
                await page.select_option('#inPopupServiceKindId', value='4')  # 訪問看護
                await page.wait_for_timeout(200)
                await page.select_option('#inPopupEstimate1', label='通常の算定')
                await page.wait_for_timeout(200)
                
                # 3. 职员类型选择（参考文件中的顺序）
                if len(row) > 27:
                    staff_type = row[27]
                    if staff_type == "正看護師":
                        await page.select_option('#inPopupEstimate3', label='正看護師')
                    elif staff_type == "准看護師":
                        await page.select_option('#inPopupEstimate3', label='准看護師')
                    elif staff_type in ["理学療法士", "言語聴覚士", "作業療法士"]:
                        await page.select_option('#inPopupEstimate3', label='作業療法士・理学療法士・言語聴覚士')
                
                # 4. 基本療養費（参考文件中的顺序）
                if len(row) > 17:
                    await page.select_option('#inPopupEstimate4', label=row[17])
                if len(row) > 18:
                    await page.select_option('#inPopupEstimate5', label=row[18])
                
                # 5. 同一日訪問人数（参考文件中的顺序）
                if len(row) > 34 and row[34] == "2":
                    await page.click('#inPopupserviceContentId1')
                    await page.wait_for_timeout(2000)
                
                # 6. 開始・終了時間（参考文件中的顺序）
                if len(row) > 8:
                    await page.select_option('#inPopupStartHour', label=row[8])
                if len(row) > 9:
                    await page.select_option('#inPopupStartMinute1', label=row[9])
                if len(row) > 10:
                    await page.select_option('#inPopupStartMinute2', label=row[10])
                if len(row) > 12:
                    await page.select_option('#inPopupEndHour', label=row[12])
                if len(row) > 13:
                    await page.select_option('#inPopupEndMinute1', label=row[13])
                if len(row) > 14:
                    await page.select_option('#inPopupEndMinute2', label=row[14])
                
            else:
                # 介護予防
                await page.select_option('#inPopupServiceKindId', value='18')  # 介護予防あり
                
                # 3. 职员类型选择（介護予防使用不同字段）
                if len(row) > 27:
                    staff_type = row[27]
                    if staff_type == "正看護師":
                        await page.select_option('#inPopupEstimate2', label='正看護師')
                    elif staff_type == "准看護師":
                        await page.select_option('#inPopupEstimate2', label='准看護師')
                    elif staff_type in ["理学療法士", "言語聴覚士", "作業療法士"]:
                        await page.select_option('#inPopupEstimate2', label='作業療法士・理学療法士・言語聴覚士')
                
                # 4. 基本療養費（介護予防使用不同字段）
                if len(row) > 17:
                    await page.select_option('#inPopupEstimate3', label=row[17])
                if len(row) > 18:
                    await page.select_option('#inPopupEstimate4', label=row[18])
                
                # 5. 同一日訪問人数（参考文件中的顺序）
                if len(row) > 34 and row[34] == "2":
                    await page.click('#inPopupserviceContentId1')
                    await page.wait_for_timeout(2000)
                
                # 6. 開始・終了時間（参考文件中的顺序）
                if len(row) > 8:
                    await page.select_option('#inPopupStartHour', label=row[8])
                if len(row) > 9:
                    await page.select_option('#inPopupStartMinute1', label=row[9])
                if len(row) > 10:
                    await page.select_option('#inPopupStartMinute2', label=row[10])
                if len(row) > 12:
                    await page.select_option('#inPopupEndHour', label=row[12])
                if len(row) > 13:
                    await page.select_option('#inPopupEndMinute1', label=row[13])
                if len(row) > 14:
                    await page.select_option('#inPopupEndMinute2', label=row[14])
            
            # 7. 予定・実績（参考文件中的顺序）
            await page.click('#inPopupPlanAchievementsDivision02')
            await page.wait_for_timeout(2000)

            # 8. 选择实施日 (在实绩选择后) - 🔧 修复：使用HTML表格结构选择
            await self._select_service_date_by_table_structure(page, row)
            
            # 9. 職員情報
            await page.click('#input_staff_on .btn')
            await page.wait_for_timeout(2000)

            if len(row) > 27:
                staff_type = row[27]
                if staff_type == "正看護師":
                    await page.select_option('#chargeStaff1JobDivision1', label='看護師')
                else:
                    await page.select_option('#chargeStaff1JobDivision1', label=row[27])

            # 10. 提交表单
            await page.click('#btnRegisPop')
            await page.wait_for_timeout(2000)
            
            logger.info("✅ 介護保险处理完成（完全按照参考文件顺序）")
            
        except Exception as e:
            logger.error(f"❌ 介護保险处理失败: {e}")
            raise

    async def _process_iryou_insurance_reference(self, row: List):
        """🔧 修复医療保险处理流程 - 先打开表单再填写"""
        page = self.selector_executor.page

        try:
            logger.info("🏥 修复医療保险处理流程 - 按正确顺序填写")

            # 🔧 修复：首先点击新規追加按钮打开表单
            logger.debug("🔘 点击新規追加按钮打开表单...")
            try:
                # 🆕 使用指定的新规追加选择器配置（优先级最高）
                add_button_selectors = [
                    '#btn_area .cf:nth-child(1) :nth-child(1)',  # 🆕 指定的新选择器（最高优先级）
                    '#btnAdd',
                    '.btn-add',
                    'input[value="新規追加"]',
                    'button:has-text("新規追加")',
                    '[onclick*="add"]'
                ]

                button_clicked = False
                for selector in add_button_selectors:
                    try:
                        await page.wait_for_selector(selector, timeout=5000)
                        await page.click(selector)
                        # 🚀 关键优化：减少等待时间，表单会快速出现
                        await page.wait_for_timeout(500)  # 从2000ms减少到500ms
                        logger.debug(f"✅ 成功点击新規追加按钮: {selector}")
                        button_clicked = True
                        break
                    except Exception as e:
                        logger.debug(f"⚠️ 选择器 {selector} 失败: {e}")
                        continue

                if not button_clicked:
                    logger.warning("⚠️ 无法找到新規追加按钮，尝试继续处理")

            except Exception as e:
                logger.warning(f"⚠️ 点击新規追加按钮失败: {e}")

            # 🔧 修复：等待表单加载并验证医療保险按钮是否可见
            logger.debug("⏳ 等待表单加载...")
            try:
                await page.wait_for_selector('#inPopupInsuranceDivision02', timeout=10000)
                is_visible = await page.is_visible('#inPopupInsuranceDivision02')
                if not is_visible:
                    logger.warning("⚠️ 医療保险按钮不可见，尝试强制显示表单")
                    # 尝试执行JavaScript来显示表单
                    await page.evaluate("""
                        () => {
                            const modal = document.querySelector('#registModal');
                            if (modal) {
                                modal.style.display = 'block';
                                modal.style.visibility = 'visible';
                            }
                        }
                    """)
                    await page.wait_for_timeout(1000)
            except Exception as e:
                logger.warning(f"⚠️ 等待表单加载失败: {e}")

            # 1. 点击医療保险按钮
            logger.debug("🏥 点击医療保险按钮...")
            await page.click('#inPopupInsuranceDivision02')
            await page.wait_for_timeout(2000)
            logger.debug("✅ 已选择医療保险")

            # 2. 选择服务类型 (必须先选择)
            await page.select_option('#inPopupEstimate1', label='訪問看護')
            await page.wait_for_timeout(500)
            logger.debug("✅ 已选择服务类型: 訪問看護")

            # 3. 选择等级 (必须在服务类型之后)
            if len(row) > 32 and row[32]:
                await page.select_option('#inPopupEstimate2', label=row[32])
                await page.wait_for_timeout(500)
                logger.debug(f"✅ 已选择等级: {row[32]}")

            # 4. 职员类型选择 (必须在等级之后)
            if len(row) > 27 and row[27]:
                staff_type = row[27]
                if staff_type == "正看護師":
                    await page.select_option('#inPopupEstimate3', label='看護師等')
                elif staff_type == "准看護師":
                    await page.select_option('#inPopupEstimate3', label='准看護師')
                elif staff_type in ["理学療法士", "言語聴覚士", "作業療法士"]:
                    await page.select_option('#inPopupEstimate3', label='理学療法士等')
                await page.wait_for_timeout(500)
                logger.debug(f"✅ 已选择职员类型: {staff_type}")

            # 5. 等级特定信息 (在职员类型之后)
            if len(row) > 32 and row[32] == "Ⅱ" and len(row) > 33 and row[33]:
                await page.select_option('#inPopupEstimate4', label=row[33])
                await page.wait_for_timeout(500)
                logger.debug(f"✅ 已选择等级特定信息: {row[33]}")

            # 6. 填写時間信息 (在所有选择项之后)
            logger.debug("📅 开始填写时间信息...")
            if len(row) > 8 and row[8]:
                await page.select_option('#inPopupStartHour', label=row[8])
                await page.wait_for_timeout(200)
            if len(row) > 9 and row[9]:
                await page.select_option('#inPopupStartMinute1', label=row[9])
                await page.wait_for_timeout(200)
            if len(row) > 10 and row[10]:
                await page.select_option('#inPopupStartMinute2', label=row[10])
                await page.wait_for_timeout(200)
            if len(row) > 12 and row[12]:
                await page.select_option('#inPopupEndHour', label=row[12])
                await page.wait_for_timeout(200)
            if len(row) > 13 and row[13]:
                await page.select_option('#inPopupEndMinute1', label=row[13])
                await page.wait_for_timeout(200)
            if len(row) > 14 and row[14]:
                await page.select_option('#inPopupEndMinute2', label=row[14])
                await page.wait_for_timeout(200)
            logger.debug("✅ 时间信息填写完成")

            # 7. 选择实绩 (在所有基础字段填写完成后)
            logger.debug("📊 选择实绩...")
            await page.click('#inPopupPlanAchievementsDivision02')
            await page.wait_for_timeout(1000)
            logger.debug("✅ 已选择实绩")

            # 8. 选择实施日 (在实绩选择后) - 🔧 修复：使用HTML表格结构选择
            await self._select_service_date_by_table_structure(page, row)

            # 9. 职员信息填写 (最后步骤)
            logger.debug("👨‍⚕️ 开始填写职员信息...")
            await page.click('#input_staff_on .btn')
            await page.wait_for_timeout(2000)

            if len(row) > 27 and row[27]:
                staff_type = row[27]
                try:
                    if staff_type == "正看護師":
                        await page.select_option('#chargeStaff1JobDivision1', label='看護師')
                    else:
                        await page.select_option('#chargeStaff1JobDivision1', label=row[27])
                    logger.debug(f"✅ 职员职种填写完成: {staff_type}")
                except Exception as e:
                    logger.warning(f"⚠️ 职员职种填写失败: {e}")

            # 10. 提交表单
            logger.debug("📤 提交表单...")
            await page.click('#btnRegisPop')
            await page.wait_for_timeout(2000)

            logger.info("✅ 医療保险处理完成 - 修复版")

        except Exception as e:
            logger.error(f"❌ 医療保险处理失败: {e}")
            raise

    async def _process_seishin_iryou_insurance_reference(self, row: List):
        """🆕 参考RPA代码处理精神医療保险"""
        page = self.selector_executor.page
        
        try:
            logger.info("🏥 参考RPA代码处理精神医療保险")
            
            # 1. 点击医療保险按钮
            await page.click('#inPopupInsuranceDivision02')
            await page.wait_for_timeout(3000)
            
            # 2. 选择服务类型和等级
            await page.select_option('#inPopupEstimate1', label='精神科訪問看護')
            if len(row) > 32:
                await page.select_option('#inPopupEstimate2', label=row[32])
            
            # 3. 职员类型选择
            if len(row) > 27:
                staff_type = row[27]
                if staff_type == "正看護師":
                    await page.select_option('#inPopupEstimate3', label='看護師等')
                elif staff_type == "准看護師":
                    await page.select_option('#inPopupEstimate3', label='准看護師')
                elif staff_type in ["理学療法士", "言語聴覚士", "作業療法士"]:
                    await page.select_option('#inPopupEstimate3', label='作業療法士')
            
            # 4. 等级特定信息
            if len(row) > 32 and row[32] == "Ⅲ":
                if len(row) > 33:
                    await page.select_option('#inPopupEstimate4', label=row[33])
            
            # 5. 时间信息填写
            if len(row) > 8:
                await page.select_option('#inPopupStartHour', label=row[8])
            if len(row) > 9:
                await page.select_option('#inPopupStartMinute1', label=row[9])
            if len(row) > 10:
                await page.select_option('#inPopupStartMinute2', label=row[10])
            if len(row) > 12:
                await page.select_option('#inPopupEndHour', label=row[12])
            if len(row) > 13:
                await page.select_option('#inPopupEndMinute1', label=row[13])
            if len(row) > 14:
                await page.select_option('#inPopupEndMinute2', label=row[14])
            
            # 6. 实绩选择
            await page.click('#inPopupPlanAchievementsDivision02')
            await page.wait_for_timeout(1000)

            # 7. 选择实施日 (在实绩选择后) - 🔧 修复：使用HTML表格结构选择
            await self._select_service_date_by_table_structure(page, row)
            
            # 8. 职员信息
            await page.click('#input_staff_on .btn')
            await page.wait_for_timeout(2000)

            if len(row) > 27:
                staff_type = row[27]
                if staff_type == "正看護師":
                    await page.select_option('#chargeStaff1JobDivision1', label='看護師')
                else:
                    await page.select_option('#chargeStaff1JobDivision1', label=row[27])

            # 9. 提交表单
            await page.click('#btnRegisPop')
            await page.wait_for_timeout(2000)
            
            logger.info("✅ 精神医療保险处理完成（参考RPA代码）")

        except Exception as e:
            logger.error(f"❌ 精神医療保险处理失败: {e}")
            raise

    async def _select_service_date_by_table_structure(self, page, row: List):
        """🆕 根据HTML表格结构选择实施日（不使用row[28]数据点击）"""
        try:
            logger.info("📅 开始根据HTML表格结构选择实施日...")

            # 🆕 从G列（第6列，索引为6）获取实施日数据
            service_date = None
            if len(row) > 6 and row[6]:
                service_date = str(row[6]).strip()
                logger.info(f"📅 从G列获取实施日数据: '{service_date}'")
            else:
                logger.warning("⚠️ G列没有实施日数据，使用当前日期")
                from datetime import datetime
                current_date = datetime.now()
                service_date = f"{current_date.year}/{current_date.month:02d}/{current_date.day:02d}"

            # 🆕 解析日期数据
            parsed_date = self._parse_service_date(service_date)
            if not parsed_date:
                logger.warning(f"⚠️ 无法解析实施日数据: {service_date}")
                return

            year, month, day = parsed_date
            logger.info(f"📅 解析后的日期: {year}年{month}月{day}日")

            # 🆕 等待日历组件加载
            await self._wait_for_calendar_ready(page)

            # 🆕 使用HTML表格结构选择日期
            success = await self._click_date_by_table_structure(page, year, month, day)

            if success:
                logger.info("✅ 实施日选择成功")
                await page.wait_for_timeout(2000)  # 等待UI更新
            else:
                logger.warning("⚠️ 实施日选择失败，尝试备用方法")
                await self._fallback_date_selection(page, day)

        except Exception as e:
            logger.error(f"❌ 实施日选择异常: {e}")
            # 不抛出异常，继续后续流程

    def _parse_service_date(self, date_str: str) -> tuple:
        """解析实施日字符串，返回(年, 月, 日)"""
        import re

        try:
            # 支持多种日期格式
            patterns = [
                r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})',  # 2025/08/01 或 2025-08-01
                r'(\d{1,2})[/-](\d{1,2})[/-](\d{4})',  # 08/01/2025 或 08-01-2025
                r'(\d{4})年(\d{1,2})月(\d{1,2})日',     # 2025年8月1日
                r'(\d{1,2})月(\d{1,2})日',              # 8月1日（使用当前年份）
            ]

            for pattern in patterns:
                match = re.match(pattern, date_str.strip())
                if match:
                    groups = match.groups()

                    if len(groups) == 3:
                        if '年' in pattern:  # 日文格式
                            year, month, day = int(groups[0]), int(groups[1]), int(groups[2])
                        elif pattern.startswith(r'(\d{4})'):  # 年在前
                            year, month, day = int(groups[0]), int(groups[1]), int(groups[2])
                        else:  # 年在后
                            month, day, year = int(groups[0]), int(groups[1]), int(groups[2])
                    elif len(groups) == 2:  # 只有月日，使用当前年份
                        from datetime import datetime
                        current_year = datetime.now().year
                        year, month, day = current_year, int(groups[0]), int(groups[1])

                    return (year, month, day)

            # 如果都不匹配，尝试提取数字
            numbers = re.findall(r'\d+', date_str)
            if len(numbers) >= 3:
                # 假设是年月日顺序
                year, month, day = int(numbers[0]), int(numbers[1]), int(numbers[2])
                # 验证年份合理性
                if year < 100:  # 如果年份小于100，可能是两位数年份
                    year += 2000
                return (year, month, day)
            elif len(numbers) == 2:
                # 假设是月日，使用当前年份
                from datetime import datetime
                current_year = datetime.now().year
                month, day = int(numbers[0]), int(numbers[1])
                return (current_year, month, day)

        except Exception as e:
            logger.debug(f"⚠️ 日期解析异常: {e}")

        return None

    async def _wait_for_calendar_ready(self, page):
        """等待日历组件准备就绪"""
        try:
            logger.debug("🗓️ 等待日历组件准备就绪...")

            # 等待日历容器出现
            await page.wait_for_selector('#simple-select-days-range', timeout=10000)

            # 等待表格结构加载
            await page.wait_for_selector('#simple-select-days-range table tbody', timeout=5000)

            # 额外等待确保日历完全渲染
            await page.wait_for_timeout(1000)

            logger.debug("✅ 日历组件已准备就绪")

        except Exception as e:
            logger.warning(f"⚠️ 等待日历组件失败: {e}")

    async def _click_date_by_table_structure(self, page, year: int, month: int, day: int) -> bool:
        """🆕 根据HTML表格结构点击日期（按照你提供的HTML结构实现）"""
        try:
            logger.debug(f"🖱️ 根据表格结构点击日期: {year}年{month}月{day}日")

            # 🆕 根据你提供的HTML结构，使用data-month和data-year属性
            # data-month="7" 表示8月（JavaScript月份索引）
            js_month = month - 1  # 转换为JavaScript月份索引

            success = await page.evaluate(f"""
                () => {{
                    const targetYear = {year};
                    const targetMonth = {js_month};  // JavaScript月份索引
                    const targetDay = {day};

                    console.log('🎯 寻找日期:', targetYear + '年' + (targetMonth + 1) + '月' + targetDay + '日');
                    console.log('📅 目标data-month值:', targetMonth);

                    // 🆕 根据你提供的HTML结构查找匹配的单元格
                    const tbody = document.querySelector('#simple-select-days-range tbody');
                    if (!tbody) {{
                        console.error('❌ 未找到日历表格tbody');
                        return false;
                    }}

                    // 遍历所有行和单元格
                    const rows = tbody.querySelectorAll('tr');
                    console.log('📅 找到表格行数:', rows.length);

                    for (const row of rows) {{
                        const cells = row.querySelectorAll('td[data-handler="selectDayCustom"]');

                        for (const cell of cells) {{
                            const dataYear = cell.getAttribute('data-year');
                            const dataMonth = cell.getAttribute('data-month');
                            const link = cell.querySelector('a.ui-state-default');

                            if (link &&
                                dataYear === targetYear.toString() &&
                                dataMonth === targetMonth.toString() &&
                                !cell.classList.contains('ui-state-disabled')) {{

                                const cellText = link.textContent.trim();
                                const cellDay = parseInt(cellText, 10);

                                console.log('🔍 检查单元格:', {{
                                    text: cellText,
                                    day: cellDay,
                                    year: dataYear,
                                    month: dataMonth,
                                    target: targetDay,
                                    disabled: cell.classList.contains('ui-state-disabled')
                                }});

                                if (cellDay === targetDay) {{
                                    console.log('✅ 找到匹配日期，点击:', cellText);

                                    // 🆕 增强的点击事件触发
                                    try {{
                                        // 1. 移除其他选中状态
                                        document.querySelectorAll('#simple-select-days-range .ui-state-active').forEach(el => {{
                                            el.classList.remove('ui-state-active');
                                        }});

                                        // 2. 添加选中状态
                                        link.classList.add('ui-state-active');
                                        cell.classList.add('ui-state-active');

                                        // 3. 触发点击事件
                                        const clickEvent = new MouseEvent('click', {{
                                            bubbles: true,
                                            cancelable: true,
                                            view: window
                                        }});

                                        link.dispatchEvent(clickEvent);

                                        // 4. 如果有jQuery，也触发jQuery事件
                                        if (window.jQuery && window.jQuery(link).length > 0) {{
                                            window.jQuery(link).trigger('click');
                                        }}

                                        // 5. 直接调用click方法作为备用
                                        link.click();

                                        console.log('✅ 日期点击事件已触发');
                                        return true;

                                    }} catch (error) {{
                                        console.error('❌ 点击事件触发失败:', error);
                                        return false;
                                    }}
                                }}
                            }}
                        }}
                    }}

                    console.log('❌ 未找到匹配的日期单元格');
                    return false;
                }}
            """)

            if success:
                logger.debug("✅ 表格结构日期点击成功")
                return True
            else:
                logger.warning("⚠️ 表格结构日期点击失败")
                return False

        except Exception as e:
            logger.error(f"❌ 表格结构日期点击异常: {e}")
            return False

    async def _fallback_date_selection(self, page, day: int):
        """备用日期选择方法"""
        try:
            logger.debug(f"🔄 使用备用方法选择日期: {day}日")

            # 备用策略1：选择第一个可用的匹配日期
            success = await page.evaluate(f"""
                () => {{
                    const targetDay = {day};

                    // 查找所有可点击的日期链接
                    const dateLinks = document.querySelectorAll('#simple-select-days-range td:not(.ui-state-disabled) a.ui-state-default');

                    for (const link of dateLinks) {{
                        const cellText = link.textContent.trim();
                        const cellDay = parseInt(cellText, 10);

                        if (cellDay === targetDay) {{
                            console.log('✅ 备用方法找到匹配日期:', cellText);
                            link.click();
                            return true;
                        }}
                    }}

                    // 如果找不到匹配日期，选择第一个可用日期
                    if (dateLinks.length > 0) {{
                        console.log('⚠️ 未找到匹配日期，选择第一个可用日期');
                        dateLinks[0].click();
                        return true;
                    }}

                    return false;
                }}
            """)

            if success:
                logger.debug("✅ 备用日期选择成功")
                await page.wait_for_timeout(1000)
            else:
                logger.warning("⚠️ 备用日期选择也失败")

        except Exception as e:
            logger.warning(f"⚠️ 备用日期选择异常: {e}")

    async def _create_selective_event_interceptor(self, page, mode="time"):
        """🆕 增强事件拦截器 - 避免过度拦截"""
        try:
            logger.debug(f"🛡️ 创建增强事件拦截器 (模式: {mode})")
            
            # 🆕 更精确的事件拦截，避免影响必要的表单验证
            await page.evaluate(f"""
                () => {{
                    const mode = '{mode}';
                    
                    // 🆕 保护关键表单事件，但允许必要的验证
                    const protectedEvents = ['beforeunload', 'unload'];
                    const allowedValidationEvents = ['blur', 'focus', 'input', 'change'];
                    
                    // 只拦截可能导致页面跳转或数据丢失的事件
                    protectedEvents.forEach(eventType => {{
                        window.addEventListener(eventType, function(e) {{
                            const registModal = document.querySelector('#registModal');
                            if (registModal && registModal.style.display !== 'none') {{
                                console.log('🛡️ 拦截可能导致数据丢失的事件:', eventType);
                                e.preventDefault();
                                e.stopPropagation();
                                return false;
                            }}
                        }}, true);
                    }});
                    
                    // 🆕 智能拦截：只在特定条件下拦截时间相关函数
                    if (mode === 'time') {{
                        const originalPopulateEndTime = window.populateEstimationEndTime;
                        window.populateEstimationEndTime = function() {{
                            const registModal = document.querySelector('#registModal');
                            const hasUserInput = document.querySelector('#estimationEndTime')?.value;
                            
                            // 只有在用户已输入数据时才拦截自动填充
                            if (registModal && registModal.style.display !== 'none' && hasUserInput) {{
                                console.log('🛡️ 拦截自动时间填充，保护用户输入');
                                return;
                            }}
                            
                            // 否则允许正常执行
                            if (originalPopulateEndTime) {{
                                return originalPopulateEndTime.apply(this, arguments);
                            }}
                        }};
                    }}
                    
                    console.log('🛡️ 增强事件拦截器已激活 (模式: ' + mode + ')');
                }}
            """)
            
        except Exception as e:
            logger.warning(f"⚠️ 创建增强事件拦截器失败: {e}")


class TennkiWorkflowManager:
    """🆕 增强工作流管理器 - 智能浏览器管理"""

    def __init__(self, workflow_config: dict):
        self.workflow_config = workflow_config
        self.smart_browser_manager = SmartBrowserManager()
        self.facility_processors = {}
        self.global_failed_data_collector = TennkiFailedDataCollector()

    async def run_workflow(self):
        """运行增强工作流"""
        logger.info("🚀 启动 Kaipoke Tennki 增强工作流")
        
        try:
            # 初始化智能浏览器管理器
            await self.smart_browser_manager.initialize()

            # 获取据点配置
            facility_configs = self.workflow_config.get('facilities', [])
            
            if not facility_configs:
                logger.warning("⚠️ 没有配置据点信息")
                return False

            # 处理每个据点
            for facility_config in facility_configs:
                try:
                    processor = EnhancedTennkiFacilityProcessor(
                        facility_config, 
                        self.workflow_config, 
                        self.smart_browser_manager
                    )
                    
                    await self._process_facility_with_error_handling(processor)
                    
                except Exception as e:
                    logger.error(f"❌ 据点处理失败: {e}")

            # 收集所有失败数据
            await self._collect_all_failed_data()

            # 导出失败数据
            await self._export_failed_data()

            return True

        except Exception as e:
            logger.error(f"❌ 工作流执行失败: {e}")
            return False
        finally:
            # 生成失败数据报告
            await self._generate_detailed_failed_data_report()

            # 清理所有资源
            await self._cleanup_all_resources()

    async def _process_facility_with_error_handling(self, processor: EnhancedTennkiFacilityProcessor):
        """带错误处理的据点处理"""
        facility_name = processor.facility_name
        try:
            # 🆕 第一步：初始化处理器（不启动浏览器）
            await processor.initialize()
            
            # 🆕 第二步：处理据点数据（会根据数据量决定是否启动浏览器）
            await processor.process_facility_data()
            
            logger.info(f"✅ 据点 {facility_name} 处理成功")
        except Exception as e:
            logger.error(f"❌ 据点 {facility_name} 处理异常: {e}", exc_info=True)
            return False
        finally:
            try:
                await processor.cleanup()
            except Exception as e:
                logger.warning(f"⚠️ 据点 {facility_name} 清理时出错: {e}")

    async def _collect_all_failed_data(self):
        """收集所有据点的失败数据"""
        for processor in self.facility_processors.values():
            if hasattr(processor, 'failed_data_collector'):
                collector = processor.failed_data_collector
                self.global_failed_data_collector.failed_records.extend(collector.failed_records)
                self.global_failed_data_collector.failed_users.extend(collector.failed_users)

    async def _export_failed_data(self):
        """🆕 导出失败数据（增强详细输出）"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            failed_data_file = f"failed_data_tennki_{timestamp}.json"
            
            # 🆕 增强失败数据收集
            failed_data = {
                'timestamp': timestamp,
                'failed_records': self.global_failed_data_collector.failed_records,
                'failed_users': self.global_failed_data_collector.failed_users,
                'summary': {
                    'total_failed_records': len(self.global_failed_data_collector.failed_records),
                    'total_failed_users': len(self.global_failed_data_collector.failed_users)
                }
            }
            
            import json
            with open(failed_data_file, 'w', encoding='utf-8') as f:
                json.dump(failed_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📄 失败数据已导出到: {failed_data_file}")
            
            # 🆕 增强失败数据日志输出
            self._print_detailed_failed_data_log()
            
        except Exception as e:
            logger.error(f"❌ 导出失败数据时出错: {e}")

    def _print_detailed_failed_data_log(self):
        """🆕 打印详细的失败数据日志（便于手动登录）"""
        logger.info("=" * 80)
        logger.info("📋 失败数据详细报告（便于手动登录）")
        logger.info("=" * 80)

        # 用户级别失败
        if self.global_failed_data_collector.failed_users:
            logger.info(f"\n👥 失败用户列表 ({len(self.global_failed_data_collector.failed_users)} 个):")
            for i, user in enumerate(self.global_failed_data_collector.failed_users, 1):
                logger.info(f"  {i}. 据点: {user['facility_name']}")
                logger.info(f"     用户: {user['user_name']}")
                logger.info(f"     错误: {user['error_message']}")
                logger.info(f"     时间: {user['timestamp']}")
                logger.info("")

        # 记录级别失败
        if self.global_failed_data_collector.failed_records:
            logger.info(f"\n📝 失败记录列表 ({len(self.global_failed_data_collector.failed_records)} 条):")
            for i, record in enumerate(self.global_failed_data_collector.failed_records, 1):
                logger.info(f"  {i}. 据点: {record['facility_name']}")
                logger.info(f"     用户: {record['user_name']}")
                logger.info(f"     行号: {record['row_index']}")
                logger.info(f"     保险: {record['insurance_type']}")
                logger.info(f"     实施日: {record['service_date']}")
                logger.info(f"     开始时间: {record['start_time']}")
                logger.info(f"     结束时间: {record['end_time']}")
                logger.info(f"     错误: {record['error_message']}")
                logger.info(f"     时间: {record['timestamp']}")
                logger.info(f"     原始数据: {record.get('raw_data', [])}")
                logger.info("")

        # 按据点统计
        facility_stats = {}
        for record in self.global_failed_data_collector.failed_records:
            facility = record['facility_name']
            if facility not in facility_stats:
                facility_stats[facility] = 0
            facility_stats[facility] += 1

        for user in self.global_failed_data_collector.failed_users:
            facility = user['facility_name']
            if facility not in facility_stats:
                facility_stats[facility] = 0
            facility_stats[facility] += 1

        if facility_stats:
            logger.info("📊 按据点失败统计:")
            for facility, count in facility_stats.items():
                logger.info(f"  - {facility}: {count} 条失败")

        # 🆕 增强：按保险类型统计
        insurance_stats = {}
        for record in self.global_failed_data_collector.failed_records:
            insurance = record.get('insurance_type', 'unknown')
            if insurance not in insurance_stats:
                insurance_stats[insurance] = 0
            insurance_stats[insurance] += 1

        if insurance_stats:
            logger.info("🏥 按保险类型失败统计:")
            for insurance, count in insurance_stats.items():
                logger.info(f"  - {insurance}: {count} 条失败")

        # 🆕 增强：按错误类型统计
        error_stats = {}
        for record in self.global_failed_data_collector.failed_records:
            error_msg = record.get('error_message', 'unknown')
            # 提取错误类型（前50个字符）
            error_type = error_msg[:50] + "..." if len(error_msg) > 50 else error_msg
            if error_type not in error_stats:
                error_stats[error_type] = 0
            error_stats[error_type] += 1

        if error_stats:
            logger.info("❌ 按错误类型统计:")
            for error_type, count in error_stats.items():
                logger.info(f"  - {error_type}: {count} 条失败")

        logger.info("=" * 80)

    async def _cleanup_all_resources(self):
        """清理所有资源"""
        try:
            await self.smart_browser_manager.close_all_browsers()
            logger.info("🔒 所有资源清理完成")
        except Exception as e:
            logger.warning(f"⚠️ 资源清理时出错: {e}")

    async def _generate_detailed_failed_data_report(self):
        """🆕 生成详细的失败数据报告"""
        try:
            logger.info("================================================================================")
            logger.info("📋 失败数据详细报告（便于手动登录）")
            logger.info("================================================================================")

            # 收集所有处理器的失败数据
            all_failed_data = []

            for processor in self.facility_processors:
                if hasattr(processor, 'failed_data_collector') and processor.failed_data_collector:
                    # 获取失败记录和失败用户
                    failed_records = processor.failed_data_collector.failed_records
                    failed_users = processor.failed_data_collector.failed_users

                    # 转换为统一格式
                    for record in failed_records:
                        unified_record = {
                            'data': record.get('raw_data', []),
                            'error': record.get('error_message', ''),
                            'context': {
                                'user_name': record.get('user_name', ''),
                                'facility_name': record.get('facility_name', ''),
                                'insurance_type': record.get('insurance_type', ''),
                                'timestamp': record.get('timestamp', '')
                            }
                        }
                        all_failed_data.append(unified_record)

                    for user in failed_users:
                        unified_record = {
                            'data': [],
                            'error': user.get('error_message', ''),
                            'context': {
                                'user_name': user.get('user_name', ''),
                                'facility_name': user.get('facility_name', ''),
                                'insurance_type': '用户级别失败',
                                'timestamp': user.get('timestamp', '')
                            }
                        }
                        all_failed_data.append(unified_record)

            if not all_failed_data:
                logger.info("✅ 没有失败的数据记录")
                logger.info("================================================================================")
                return

            logger.info(f"❌ 总共有 {len(all_failed_data)} 条数据处理失败")
            logger.info("")

            # 按用户分组显示失败数据
            failed_by_user = {}
            for failed_record in all_failed_data:
                user_name = failed_record.get('context', {}).get('user_name', '未知用户')
                if user_name not in failed_by_user:
                    failed_by_user[user_name] = []
                failed_by_user[user_name].append(failed_record)

            for user_name, user_failed_records in failed_by_user.items():
                logger.info(f"👤 用户: {user_name} - 失败记录: {len(user_failed_records)} 条")
                logger.info("-" * 80)

                for i, failed_record in enumerate(user_failed_records, 1):
                    record_data = failed_record.get('data', {})
                    error_msg = failed_record.get('error', '未知错误')
                    context = failed_record.get('context', {})

                    logger.info(f"  📝 记录 {i}:")
                    logger.info(f"    🏥 据点: {context.get('facility_name', '未知')}")
                    logger.info(f"    🏥 保险类型: {context.get('insurance_type', '未知')}")
                    logger.info(f"    📅 实施日: {record_data.get(6, '未知') if isinstance(record_data, list) and len(record_data) > 6 else '未知'}")
                    logger.info(f"    👨‍⚕️ 职员: {record_data.get(27, '未知') if isinstance(record_data, list) and len(record_data) > 27 else '未知'}")
                    logger.info(f"    ❌ 错误: {error_msg}")
                    logger.info(f"    🕐 时间: {context.get('timestamp', '未知')}")

                    # 显示完整的数据内容（便于手动录入）
                    if isinstance(record_data, list) and len(record_data) > 0:
                        logger.info(f"    📊 完整数据:")
                        key_fields = {
                            0: "A列(用户名)",
                            6: "G列(实施日)",
                            7: "H列(开始时间)",
                            8: "I列(结束时间)",
                            9: "J列(服务内容)",
                            27: "AB列(职员)",
                        }

                        for idx, field_name in key_fields.items():
                            if idx < len(record_data) and record_data[idx]:
                                logger.info(f"      {field_name}: {record_data[idx]}")

                    logger.info("")

                logger.info("")

            # 保存失败数据到文件
            failed_data_file = f"failed_data_tennki_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            # 准备保存的数据结构
            save_data = {
                'timestamp': datetime.now().isoformat(),
                'total_failed_records': len(all_failed_data),
                'failed_by_user': {}
            }

            for user_name, user_failed_records in failed_by_user.items():
                save_data['failed_by_user'][user_name] = []
                for failed_record in user_failed_records:
                    save_record = {
                        'error': failed_record.get('error', ''),
                        'context': failed_record.get('context', {}),
                        'data': failed_record.get('data', []),
                        'timestamp': failed_record.get('context', {}).get('timestamp', '')
                    }
                    save_data['failed_by_user'][user_name].append(save_record)

            # 保存到文件
            import json
            with open(failed_data_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)

            logger.info(f"📄 失败数据已导出到: {failed_data_file}")
            logger.info("================================================================================")

        except Exception as e:
            logger.error(f"❌ 生成失败数据报告失败: {e}")


from dotenv import load_dotenv

def run(config: dict):
    """工作流入口函数（同步版本，供main.py调用）"""
    
    async def async_run():
        """异步执行函数"""
        # 环境变量加载
        load_dotenv()
        
        # 使用传入的config参数
        current_config = config
        logger.debug(f"CONFIG: {current_config}")
        
        # 确保 facilities 列表存在
        facilities = current_config.get('facilities') or current_config.get('config', {}).get('facilities')
        if not facilities:
            logger.error("❌ 工作流配置中缺少 'facilities' 部分。")
            return False
        
        # 更新配置结构以确保兼容性
        if 'config' in current_config:
            # 如果配置是嵌套结构，提取内部配置
            current_config = current_config['config']

        # 验证每个facility都有必要的配置
        for i, facility in enumerate(current_config['facilities']):
            if 'spreadsheet_id' not in facility or not facility['spreadsheet_id']:
                logger.error(f"❌ 据点 {i+1} 缺少 spreadsheet_id 配置")
                return False
            logger.info(f"✅ 据点配置验证: {facility['name']} -> {facility['spreadsheet_id']}")

        # 创建并运行工作流管理器
        workflow_manager = TennkiWorkflowManager(current_config)
        success = await workflow_manager.run_workflow()
        
        if success:
            logger.info("🎉 所有据点处理成功！")
        else:
            logger.warning("⚠️ 部分据点处理失败，请检查日志")
        
        return success

    asyncio.run(async_run())


if __name__ == "__main__":
    # 直接运行时，需要一个模拟的 config
    # 这部分主要用于直接测试此脚本
    from configs.workflows import WORKFLOWS
    
    # 假设我们要测试 'kaipoke_tennki_refactored'
    test_workflow_id = 'kaipoke_tennki_refactored'
    if test_workflow_id in WORKFLOWS:
        test_config = WORKFLOWS[test_workflow_id]
        run(test_config)
    else:
        logger.error(f"测试工作流ID '{test_workflow_id}' 在 workflows.yaml 中未找到。")
