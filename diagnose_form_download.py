#!/usr/bin/env python3
"""
Kaipoke Form Download 诊断脚本
用于检查配置和选择器是否正确
"""

import os
import sys
import yaml
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from logger_config import logger

def check_selectors_config():
    """检查选择器配置"""
    logger.info("🔍 检查选择器配置...")
    
    try:
        selectors_path = Path("configs/selectors.yaml")
        with open(selectors_path, 'r', encoding='utf-8') as f:
            selectors = yaml.safe_load(f)
        
        # 检查kaipoke_form_download配置
        if 'kaipoke_form_download' not in selectors:
            logger.error("❌ selectors.yaml中缺少kaipoke_form_download配置")
            return False
        
        form_config = selectors['kaipoke_form_download']
        
        # 检查navigation配置
        if 'navigation' not in form_config:
            logger.error("❌ kaipoke_form_download中缺少navigation配置")
            return False
        
        navigation = form_config['navigation']
        
        # 检查所有nav_group
        required_nav_groups = ['nav_group_1', 'nav_group_2', 'nav_group_3', 
                              'nav_group_4', 'nav_group_5', 'nav_group_6']
        
        for nav_group in required_nav_groups:
            if nav_group not in navigation:
                logger.error(f"❌ 缺少导航组: {nav_group}")
                return False
            
            nav_config = navigation[nav_group]
            if 'hover' not in nav_config or 'click' not in nav_config:
                logger.error(f"❌ 导航组 {nav_group} 缺少hover或click配置")
                return False
            
            logger.info(f"✅ 导航组 {nav_group}: hover='{nav_config['hover']}', click='{nav_config['click']}'")
        
        # 检查actions配置
        if 'actions' not in form_config:
            logger.error("❌ kaipoke_form_download中缺少actions配置")
            return False
        
        actions = form_config['actions']
        required_actions = ['month_selector_main', 'month_selector_alt', 
                           'print_button_1', 'download_button_main']
        
        for action in required_actions:
            if action not in actions:
                logger.error(f"❌ 缺少动作配置: {action}")
                return False
            logger.info(f"✅ 动作配置 {action}: {actions[action]}")
        
        logger.info("✅ 选择器配置检查通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 选择器配置检查失败: {e}")
        return False

def check_workflow_config():
    """检查工作流配置"""
    logger.info("🔍 检查工作流配置...")
    
    try:
        config_path = Path("configs/workflows.yaml")
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        if 'kaipoke_form_download' not in config:
            logger.error("❌ workflows.yaml中缺少kaipoke_form_download配置")
            return False
        
        workflow = config['kaipoke_form_download']
        
        # 检查基本配置
        if 'config' not in workflow:
            logger.error("❌ 缺少config配置")
            return False
        
        if 'tasks' not in workflow:
            logger.error("❌ 缺少tasks配置")
            return False
        
        tasks = workflow['tasks']
        logger.info(f"✅ 找到 {len(tasks)} 个任务")
        
        # 检查前几个任务的配置
        for i, task in enumerate(tasks[:3]):
            task_id = task.get('task_id', f'task_{i}')
            logger.info(f"📋 任务 {task_id}:")
            logger.info(f"  - element_text: {task.get('element_text')}")
            logger.info(f"  - flow_pattern: {task.get('flow_pattern')}")
            logger.info(f"  - nav_group: {task.get('params', {}).get('nav_group')}")
            
            # 检查必要字段
            required_fields = ['element_text', 'flow_pattern', 'params']
            for field in required_fields:
                if field not in task:
                    logger.error(f"❌ 任务 {task_id} 缺少字段: {field}")
                    return False
        
        logger.info("✅ 工作流配置检查通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 工作流配置检查失败: {e}")
        return False

def check_environment():
    """检查环境变量"""
    logger.info("🔍 检查环境变量...")
    
    required_vars = [
        'KAIPOKE_CORPORATION_ID',
        'KAIPOKE_MEMBER_LOGIN_ID',
        'KAIPOKE_PASSWORD'
    ]
    
    missing = []
    for var in required_vars:
        if not os.getenv(var):
            missing.append(var)
        else:
            logger.info(f"✅ {var}: {'*' * len(os.getenv(var))}")
    
    if missing:
        logger.error(f"❌ 缺少环境变量: {missing}")
        return False
    
    logger.info("✅ 环境变量检查通过")
    return True

def main():
    """主函数"""
    logger.info("🚀 开始Kaipoke Form Download诊断")
    
    checks = [
        ("环境变量", check_environment),
        ("选择器配置", check_selectors_config),
        ("工作流配置", check_workflow_config)
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        logger.info(f"\n{'='*50}")
        logger.info(f"检查: {check_name}")
        logger.info(f"{'='*50}")
        
        if not check_func():
            all_passed = False
            logger.error(f"❌ {check_name} 检查失败")
        else:
            logger.info(f"✅ {check_name} 检查通过")
    
    logger.info(f"\n{'='*50}")
    if all_passed:
        logger.info("✅ 所有检查都通过，可以运行工作流")
        logger.info("运行命令: python run_form_download_test.py")
    else:
        logger.error("❌ 存在配置问题，请修复后再运行")
    logger.info(f"{'='*50}")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
