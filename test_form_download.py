#!/usr/bin/env python3
"""
Kaipoke Form Download 测试脚本
"""

import os
import sys
import yaml
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from logger_config import logger

def test_single_task():
    """测试单个任务"""
    try:
        from workflows.kaipoke_form_download import run
        
        # 创建简单的测试配置
        test_config = {
            'config': {
                'login_url': "https://r.kaipoke.biz/kaipokebiz/login/COM020102.do",
                'corporation_id_env': "KAIPOKE_CORPORATION_ID",
                'member_login_id_env': "KAIPOKE_MEMBER_LOGIN_ID",
                'password_env': "KAIPOKE_PASSWORD",
                'download_path': "/tmp/kaipoke_form_test_downloads",
                'headless': False
            },
            'tasks': [
                {
                    'task_id': "01",
                    'output_filename': "test_訪問介護鹿児島.pdf",
                    'element_text': "訪問介護/4670105586",
                    'flow_pattern': "SIMPLE_PRINT",
                    'params': {
                        'nav_group': "nav_group_1",
                        'print_selector': "print_button_1",
                        'download_selector': "download_button_main"
                    }
                }
            ]
        }
        
        logger.info("🚀 开始测试单个任务")
        run(test_config)
        logger.info("✅ 测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}", exc_info=True)

def test_special_account_task():
    """测试特殊账号任务"""
    try:
        from workflows.kaipoke_form_download import run
        
        # 创建特殊账号测试配置
        test_config = {
            'config': {
                'login_url': "https://r.kaipoke.biz/kaipokebiz/login/COM020102.do",
                'corporation_id_env': "KAIPOKE_CORPORATION_ID",
                'member_login_id_env': "KAIPOKE_MEMBER_LOGIN_ID",
                'password_env': "KAIPOKE_PASSWORD",
                'download_path': "/tmp/kaipoke_form_test_downloads",
                'headless': False
            },
            'tasks': [
                {
                    'task_id': "34",
                    'output_filename': "test_東千石.pdf",
                    'element_text': "通所介護/**********",
                    'flow_pattern': "SIMPLE_PRINT",
                    'corporation_id_env': "KAIPOKE_HIGASHISENGOKU_CORPORATION_ID",
                    'member_login_id_env': "KAIPOKE_HIGASHISENGOKU_MEMBER_LOGIN_ID",
                    'password_env': "KAIPOKE_HIGASHISENGOKU_PASSWORD",
                    'params': {
                        'nav_group': "nav_group_5",
                        'print_selector': "print_button_1",
                        'download_selector': "download_button_main"
                    }
                }
            ]
        }
        
        logger.info("🚀 开始测试特殊账号任务")
        run(test_config)
        logger.info("✅ 测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}", exc_info=True)

def check_env_vars():
    """检查环境变量"""
    required = ['KAIPOKE_CORPORATION_ID', 'KAIPOKE_MEMBER_LOGIN_ID', 'KAIPOKE_PASSWORD']
    missing = [var for var in required if not os.getenv(var)]
    
    if missing:
        logger.error(f"❌ 缺少环境变量: {missing}")
        return False
    
    logger.info("✅ 环境变量检查通过")
    return True

def main():
    """主函数"""
    if not check_env_vars():
        return 1
    
    print("选择测试类型:")
    print("1. 测试单个普通任务")
    print("2. 测试特殊账号任务")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        test_single_task()
    elif choice == "2":
        test_special_account_task()
    else:
        print("无效选择")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
