#!/usr/bin/env python3
"""
测试据点导航修复
专门测试据点导航超时问题的修复效果
"""

import asyncio
import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from logger_config import logger
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_with_env
from core.rpa_tools.tennki_facility_manager import TennkiFacilityManager
from playwright.async_api import async_playwright


class FacilityNavigationTest:
    """据点导航测试"""

    def __init__(self):
        self.playwright = None
        self.browsers = []
        self.pages = []

    async def initialize(self):
        """初始化测试环境"""
        logger.info("🚀 初始化据点导航测试...")
        self.playwright = await async_playwright().start()

    async def create_test_browsers(self, count=2):
        """创建测试浏览器"""
        logger.info(f"🌐 创建 {count} 个测试浏览器...")
        
        for i in range(count):
            browser = await self.playwright.firefox.launch(headless=False)
            page = await browser.new_page()
            
            self.browsers.append(browser)
            self.pages.append(page)
            
            logger.info(f"✅ 浏览器 {i+1} 创建成功")

    async def test_facility_navigation_for_browser(self, browser_id: int):
        """测试单个浏览器的据点导航"""
        page = self.pages[browser_id - 1]
        
        try:
            logger.info(f"🔐 浏览器 {browser_id} 开始登录...")
            
            # 登录系统
            await kaipoke_login_with_env(
                page,
                'KAIPOKE_CORPORATION_ID',
                'KAIPOKE_MEMBER_LOGIN_ID', 
                'KAIPOKE_PASSWORD',
                'https://r.kaipoke.biz/kaipokebiz/login/COM020102.do'
            )
            
            logger.info(f"✅ 浏览器 {browser_id} 登录成功")
            
            # 等待页面加载
            await page.wait_for_load_state('networkidle', timeout=30000)
            
            # 点击レセプト主菜单
            logger.info(f"📋 浏览器 {browser_id} 点击レセプト主菜单...")
            await page.wait_for_selector('.mainCtg li:nth-of-type(1) a', timeout=10000)
            await page.click('.mainCtg li:nth-of-type(1) a')
            await page.wait_for_load_state('networkidle', timeout=30000)
            
            # 创建据点管理器并测试导航
            selector_executor = SelectorExecutor(page)
            facility_manager = TennkiFacilityManager(selector_executor)
            
            # 测试据点导航
            element_text = "訪問看護/4660190861"
            facility_name = "荒田"
            
            logger.info(f"🏥 浏览器 {browser_id} 开始导航到据点: {element_text}")
            
            # 使用修复后的导航方法
            await facility_manager.navigate_to_facility(element_text, facility_name)
            
            logger.info(f"✅ 浏览器 {browser_id} 据点导航成功")
            
            # 验证导航结果
            await self._verify_facility_page(page, browser_id)
            
            # 继续测试月間スケージュール导航
            logger.info(f"🏥 浏览器 {browser_id} 开始导航到月間スケージュール...")
            await facility_manager.navigate_to_nursing_page()
            
            logger.info(f"✅ 浏览器 {browser_id} 月間スケージュール导航成功")
            
            # 验证月間スケージュール页面
            await self._verify_monthly_schedule_page(page, browser_id)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_id} 导航测试失败: {e}")
            
            # 诊断失败原因
            await self._diagnose_failure(page, browser_id, str(e))
            
            return False

    async def _verify_facility_page(self, page, browser_id: int):
        """验证据点页面"""
        try:
            logger.info(f"🔍 浏览器 {browser_id} 验证据点页面...")
            
            # 检查当前URL
            current_url = page.url
            logger.info(f"📄 浏览器 {browser_id} 据点页面URL: {current_url}")
            
            # 检查关键元素
            key_elements = [
                ('.dropdown', '下拉菜单'),
                ('.dropdown:nth-child(3)', '第3个下拉菜单'),
                ('h1', '页面标题')
            ]
            
            found_elements = []
            for selector, description in key_elements:
                try:
                    if await page.wait_for_selector(selector, timeout=5000):
                        found_elements.append(description)
                        logger.info(f"✅ 浏览器 {browser_id} 找到: {description}")
                except:
                    logger.warning(f"⚠️ 浏览器 {browser_id} 未找到: {description}")
            
            if len(found_elements) >= 1:
                logger.info(f"✅ 浏览器 {browser_id} 据点页面验证成功")
                return True
            else:
                logger.warning(f"⚠️ 浏览器 {browser_id} 据点页面验证失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_id} 据点页面验证异常: {e}")
            return False

    async def _verify_monthly_schedule_page(self, page, browser_id: int):
        """验证月間スケージュール页面"""
        try:
            logger.info(f"🔍 浏览器 {browser_id} 验证月間スケージュール页面...")
            
            # 检查关键元素
            key_elements = [
                ('.pulldownUser', '用户选择下拉框'),
                ('#selectServiceOfferYm', '年月选择器'),
                ('text=新規追加', '新增按钮')
            ]
            
            found_elements = []
            for selector, description in key_elements:
                try:
                    if await page.wait_for_selector(selector, timeout=5000):
                        found_elements.append(description)
                        logger.info(f"✅ 浏览器 {browser_id} 找到: {description}")
                except:
                    logger.warning(f"⚠️ 浏览器 {browser_id} 未找到: {description}")
            
            if len(found_elements) >= 2:
                logger.info(f"✅ 浏览器 {browser_id} 月間スケージュール页面验证成功")
                return True
            else:
                logger.warning(f"⚠️ 浏览器 {browser_id} 月間スケージュール页面验证失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_id} 月間スケージュール页面验证异常: {e}")
            return False

    async def _diagnose_failure(self, page, browser_id: int, error_msg: str):
        """诊断失败原因"""
        try:
            logger.info(f"🔍 浏览器 {browser_id} 诊断失败原因...")
            logger.info(f"❌ 错误信息: {error_msg}")
            
            # 获取当前页面信息
            current_url = page.url
            page_title = await page.title()
            
            logger.info(f"📄 当前URL: {current_url}")
            logger.info(f"📋 页面标题: {page_title}")
            
            # 检查页面上的链接
            links_info = await page.evaluate("""
                () => {
                    const links = Array.from(document.querySelectorAll('a')).slice(0, 10);
                    return links.map(link => ({
                        text: link.textContent.trim(),
                        href: link.href,
                        visible: link.offsetParent !== null
                    })).filter(link => link.text);
                }
            """)
            
            logger.info(f"📋 页面上的链接:")
            for i, link in enumerate(links_info, 1):
                visibility = "可见" if link['visible'] else "隐藏"
                logger.info(f"  {i}. {link['text']} ({visibility})")
                
        except Exception as e:
            logger.warning(f"⚠️ 诊断失败: {e}")

    async def run_parallel_test(self):
        """运行并行测试"""
        logger.info("🚀 开始并行据点导航测试...")
        
        # 创建测试任务
        tasks = []
        for i in range(len(self.browsers)):
            task = self.test_facility_navigation_for_browser(i + 1)
            tasks.append(task)
        
        # 并行执行测试
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 分析结果
        success_count = 0
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ 浏览器 {i+1} 测试异常: {result}")
            elif result:
                success_count += 1
                logger.info(f"✅ 浏览器 {i+1} 测试成功")
            else:
                logger.warning(f"⚠️ 浏览器 {i+1} 测试失败")
        
        logger.info(f"📊 测试结果: {success_count}/{len(self.browsers)} 个浏览器成功")
        return success_count == len(self.browsers)

    async def cleanup(self):
        """清理资源"""
        logger.info("🔒 清理测试资源...")
        
        for browser in self.browsers:
            try:
                await browser.close()
            except:
                pass
        
        if self.playwright:
            try:
                await self.playwright.stop()
            except:
                pass
        
        logger.info("✅ 资源清理完成")


async def main():
    """主函数"""
    logger.info("🎯 开始据点导航修复测试")
    
    test = FacilityNavigationTest()
    
    try:
        # 初始化测试环境
        await test.initialize()
        
        # 创建测试浏览器
        await test.create_test_browsers(2)
        
        # 运行并行测试
        success = await test.run_parallel_test()
        
        if success:
            logger.info("🎉 所有浏览器据点导航测试成功！修复有效！")
        else:
            logger.warning("⚠️ 部分浏览器据点导航测试失败，需要进一步调试")
        
        # 等待用户查看结果
        input("按回车键继续...")
        
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
    finally:
        await test.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
