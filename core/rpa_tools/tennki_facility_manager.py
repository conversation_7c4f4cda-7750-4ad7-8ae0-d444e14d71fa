from typing import Optional
from logger_config import logger

class TennkiFacilityManager:
    """
    Tennki据点页面管理器（修复版 - 参考RPA代码实现）
    - 负责导航到指定据点、切换到看护页面等
    - 依赖 selector_executor 进行页面操作
    - 修复据点导航超时问题
    """

    def __init__(self, selector_executor):
        self.selector_executor = selector_executor

    async def navigate_to_facility(self, element_text: str, facility_name: Optional[str] = None):
        """
        导航到指定据点（重构版 - 多重策略fallback机制 + 超时优化）
        """
        page = self.selector_executor.page
        logger.info(f"🏥 开始导航到据点: {element_text} ({facility_name})")

        try:
            # 第一步：等待页面完全加载（优化超时时间）
            try:
                await page.wait_for_load_state('networkidle', timeout=15000)  # 减少到15秒
                logger.debug("✅ 页面加载完成")
            except Exception as e:
                logger.warning(f"⚠️ 页面加载超时，继续尝试导航: {e}")
                # 即使超时也继续尝试，可能页面已经可用

            # 🆕 等待据点选择页面的关键元素出现
            try:
                await page.wait_for_selector('a, .facility-link, .service-link', timeout=10000)
                logger.debug("✅ 据点选择页面元素已加载")
            except Exception as e:
                logger.warning(f"⚠️ 据点选择页面元素加载超时: {e}")

            # 第二步：多重策略查找据点链接
            facility_found = False
            
            # 策略1：使用智能选择器（优化超时）
            try:
                success = await self.selector_executor.smart_click(
                    workflow="kaipoke_tennki",
                    category="navigation",
                    element="facility_selection",
                    target_text=element_text
                )
                if success:
                    facility_found = True
                    logger.info(f"✅ 智能选择器成功导航到据点: {element_text}")
            except Exception as e:
                logger.debug(f"⚠️ 智能选择器失败: {e}")

            # 策略2：直接文本匹配（优化超时）
            if not facility_found:
                try:
                    await page.wait_for_selector(f"text={element_text}", timeout=5000)  # 减少超时时间
                    await page.click(f"text={element_text}")
                    facility_found = True
                    logger.info(f"✅ 文本匹配成功导航到据点: {element_text}")
                except Exception as e:
                    logger.debug(f"⚠️ 文本匹配失败: {e}")

            # 策略3：XPath查找（优化超时）
            if not facility_found:
                try:
                    xpath_selector = f"//a[contains(text(), '{element_text}')]"
                    await page.wait_for_selector(f"xpath={xpath_selector}", timeout=5000)  # 减少超时时间
                    await page.click(f"xpath={xpath_selector}")
                    facility_found = True
                    logger.info(f"✅ XPath成功导航到据点: {element_text}")
                except Exception as e:
                    logger.debug(f"⚠️ XPath查找失败: {e}")

            # 🆕 策略3.5：更宽松的XPath匹配
            if not facility_found:
                try:
                    # 提取据点ID进行匹配
                    if "/" in element_text:
                        facility_id = element_text.split("/")[-1]
                        xpath_selector = f"//a[contains(text(), '{facility_id}')]"
                        await page.wait_for_selector(f"xpath={xpath_selector}", timeout=5000)
                        await page.click(f"xpath={xpath_selector}")
                        facility_found = True
                        logger.info(f"✅ 宽松XPath成功导航到据点: {facility_id}")
                except Exception as e:
                    logger.debug(f"⚠️ 宽松XPath查找失败: {e}")
            
            # 策略4：部分文本匹配（已合并到策略3.5）

            # 策略5：JavaScript查找（增强版）
            if not facility_found:
                try:
                    js_result = await page.evaluate(f"""
                        () => {{
                            const links = Array.from(document.querySelectorAll('a'));

                            // 优先精确匹配
                            let targetLink = links.find(link =>
                                link.textContent.trim() === '{element_text}'
                            );

                            // 如果没有精确匹配，尝试包含匹配
                            if (!targetLink) {{
                                targetLink = links.find(link =>
                                    link.textContent.includes('{element_text}') ||
                                    link.href.includes('{element_text}') ||
                                    link.getAttribute('href')?.includes('{element_text}')
                                );
                            }}

                            // 如果还没有，尝试据点ID匹配
                            if (!targetLink && '{element_text}'.includes('/')) {{
                                const facilityId = '{element_text}'.split('/').pop();
                                targetLink = links.find(link =>
                                    link.textContent.includes(facilityId) ||
                                    link.href.includes(facilityId)
                                );
                            }}

                            if (targetLink) {{
                                // 确保链接可见
                                if (targetLink.offsetParent !== null) {{
                                    targetLink.scrollIntoView({{ behavior: 'smooth', block: 'center' }});
                                    setTimeout(() => targetLink.click(), 500);
                                    return true;
                                }}
                            }}
                            return false;
                        }}
                    """)
                    if js_result:
                        facility_found = True
                        await page.wait_for_timeout(1000)  # 等待点击生效
                        logger.info(f"✅ JavaScript查找成功导航到据点: {element_text}")
                except Exception as e:
                    logger.debug(f"⚠️ JavaScript查找失败: {e}")

            # 🆕 策略6：模糊匹配（最后尝试）
            if not facility_found:
                try:
                    # 尝试查找包含关键词的链接
                    keywords = element_text.replace('/', ' ').split()
                    for keyword in keywords:
                        if len(keyword) >= 3:  # 只使用长度>=3的关键词
                            try:
                                selector = f"a:has-text('{keyword}')"
                                await page.wait_for_selector(selector, timeout=3000)
                                await page.click(selector)
                                facility_found = True
                                logger.info(f"✅ 模糊匹配成功导航到据点: {keyword}")
                                break
                            except:
                                continue
                except Exception as e:
                    logger.debug(f"⚠️ 模糊匹配失败: {e}")
            
            if not facility_found:
                # 诊断信息：列出页面上所有可用的链接
                await self._diagnose_available_facilities(page)
                raise RuntimeError(f"所有策略都无法找到据点: {element_text}")

            # 第三步：智能等待据点页面加载（增强版）
            logger.info(f"⏳ 等待据点页面加载: {element_text}")

            # 🆕 使用多重等待策略，避免单一超时导致失败
            page_loaded = False

            # 策略1：等待网络空闲（减少超时时间）
            try:
                await page.wait_for_load_state('networkidle', timeout=15000)
                page_loaded = True
                logger.debug("✅ 网络空闲状态达成")
            except Exception as e:
                logger.warning(f"⚠️ 网络空闲等待超时: {e}")

            # 策略2：等待DOM加载完成
            if not page_loaded:
                try:
                    await page.wait_for_load_state('domcontentloaded', timeout=10000)
                    page_loaded = True
                    logger.debug("✅ DOM内容加载完成")
                except Exception as e:
                    logger.warning(f"⚠️ DOM加载等待超时: {e}")

            # 策略3：等待关键元素出现（据点页面的特征元素）
            if not page_loaded:
                try:
                    # 等待据点页面的特征元素
                    key_elements = [
                        '.dropdown',  # 下拉菜单
                        'h1',  # 页面标题
                        '.service-header',  # 服务头部
                        '.menu-item'  # 菜单项
                    ]

                    for element in key_elements:
                        try:
                            await page.wait_for_selector(element, timeout=5000)
                            page_loaded = True
                            logger.debug(f"✅ 找到关键元素: {element}")
                            break
                        except:
                            continue

                except Exception as e:
                    logger.warning(f"⚠️ 关键元素等待失败: {e}")

            # 策略4：强制等待（最后手段）
            if not page_loaded:
                logger.warning("⚠️ 所有等待策略都失败，使用强制等待")
                await page.wait_for_timeout(3000)
                page_loaded = True

            # 🆕 验证页面是否真正加载成功
            await self._verify_facility_page_loaded(page, element_text)

            logger.info(f"✅ 成功导航到据点: {element_text} ({facility_name})")

        except Exception as e:
            logger.error(f"❌ 导航到据点失败: {element_text} ({facility_name}): {e}")
            raise RuntimeError(f"导航到据点失败: {element_text} ({facility_name}): {e}")

    async def _verify_facility_page_loaded(self, page, element_text: str):
        """🆕 验证据点页面是否正确加载"""
        try:
            logger.debug(f"🔍 验证据点页面加载: {element_text}")

            # 检查当前URL
            current_url = page.url
            logger.debug(f"📄 当前URL: {current_url}")

            # 检查页面标题
            try:
                page_title = await page.title()
                logger.debug(f"📋 页面标题: {page_title}")
            except:
                pass

            # 检查是否有下拉菜单（据点页面的特征）
            dropdown_exists = False
            try:
                dropdown_count = await page.locator('.dropdown').count()
                if dropdown_count > 0:
                    dropdown_exists = True
                    logger.debug(f"✅ 找到 {dropdown_count} 个下拉菜单")
            except:
                pass

            # 检查是否有服务相关的元素
            service_elements = [
                'h1:has-text("訪問看護")',
                'h1:has-text("訪問介護")',
                '.service-header',
                '.dropdown:nth-child(3)'  # 第3个下拉菜单（月間スケージュール所在）
            ]

            found_service_elements = []
            for element in service_elements:
                try:
                    if await page.wait_for_selector(element, timeout=2000):
                        found_service_elements.append(element)
                except:
                    continue

            if dropdown_exists or found_service_elements:
                logger.info(f"✅ 据点页面验证成功，找到元素: {found_service_elements}")
            else:
                logger.warning(f"⚠️ 据点页面验证失败，可能未正确加载")

        except Exception as e:
            logger.warning(f"⚠️ 据点页面验证异常: {e}")

    async def _diagnose_available_facilities(self, page):
        """🆕 诊断页面上可用的据点链接"""
        try:
            logger.info("🔍 诊断页面上可用的据点链接...")
            
            available_links = await page.evaluate("""
                () => {
                    const links = Array.from(document.querySelectorAll('a'));
                    return links
                        .filter(link => link.textContent.trim() && link.href)
                        .map(link => ({
                            text: link.textContent.trim(),
                            href: link.href,
                            visible: link.offsetParent !== null
                        }))
                        .slice(0, 20); // 只显示前20个链接
                }
            """)
            
            logger.info("📋 页面上可用的链接:")
            for i, link in enumerate(available_links, 1):
                visibility = "可见" if link['visible'] else "隐藏"
                logger.info(f"  {i}. {link['text']} ({visibility})")
                logger.debug(f"     URL: {link['href']}")
                
        except Exception as e:
            logger.warning(f"⚠️ 诊断据点链接失败: {e}")

    async def navigate_to_nursing_page(self):
        """
        导航到看护页面（修复版 - 增强月間スケージュール点击逻辑）
        """
        page = self.selector_executor.page
        logger.info("🏥 开始导航到看护页面（月間スケージュール）")

        try:
            # 🆕 增强的月間スケージュール导航逻辑
            nursing_found = False

            # 策略1：使用RPA代码中的精确选择器（hover + click）- 增强版
            try:
                logger.info("🎯 策略1：使用RPA精确选择器（hover + click）")

                # 第一步：等待下拉菜单按钮出现
                await page.wait_for_selector('.dropdown:nth-child(3) .dropdown-toggle', timeout=15000)
                logger.debug("✅ 找到下拉菜单按钮")

                # 第二步：鼠标悬停到下拉菜单按钮
                await page.hover('.dropdown:nth-child(3) .dropdown-toggle')
                logger.debug("✅ 成功hover到下拉菜单")

                # 第三步：等待下拉菜单展开（增加等待时间）
                await page.wait_for_timeout(1500)

                # 第四步：等待月間スケージュール链接出现并点击
                await page.wait_for_selector('.dropdown:nth-child(3) li:nth-of-type(2) a', timeout=8000)

                # 🆕 增强点击逻辑：确保链接可见且可点击
                link_visible = await page.is_visible('.dropdown:nth-child(3) li:nth-of-type(2) a')
                if not link_visible:
                    logger.warning("⚠️ 月間スケージュール链接不可见，重新hover")
                    await page.hover('.dropdown:nth-child(3) .dropdown-toggle')
                    await page.wait_for_timeout(1000)

                await page.click('.dropdown:nth-child(3) li:nth-of-type(2) a')
                nursing_found = True
                logger.info("✅ 使用RPA精确选择器成功点击月間スケージュール")

            except Exception as e:
                logger.warning(f"⚠️ RPA精确选择器失败: {e}")

            # 策略2：JavaScript强制点击（增强版）
            if not nursing_found:
                try:
                    logger.info("🎯 策略2：JavaScript强制点击月間スケージュール")

                    js_result = await page.evaluate("""
                        () => {
                            console.log('🔍 查找月間スケージュール菜单...');

                            // 查找第3个下拉菜单
                            const dropdown = document.querySelector('.dropdown:nth-child(3)');
                            if (!dropdown) {
                                console.error('❌ 未找到第3个下拉菜单');
                                return false;
                            }

                            const toggle = dropdown.querySelector('.dropdown-toggle');
                            const menuItem = dropdown.querySelector('li:nth-of-type(2) a');

                            if (!toggle || !menuItem) {
                                console.error('❌ 未找到下拉菜单按钮或月間スケージュール链接');
                                return false;
                            }

                            console.log('✅ 找到月間スケージュール元素');
                            console.log('菜单项文本:', menuItem.textContent.trim());

                            // 强制显示下拉菜单
                            const dropdownMenu = dropdown.querySelector('.dropdown-menu');
                            if (dropdownMenu) {
                                dropdownMenu.style.display = 'block';
                                dropdownMenu.style.visibility = 'visible';
                            }

                            // 触发hover事件
                            toggle.dispatchEvent(new MouseEvent('mouseenter', {bubbles: true}));
                            toggle.dispatchEvent(new MouseEvent('mouseover', {bubbles: true}));

                            // 确保菜单项可见
                            menuItem.style.display = 'block';
                            menuItem.style.visibility = 'visible';

                            // 点击月間スケージュール
                            menuItem.click();

                            console.log('✅ 已点击月間スケージュール');
                            return true;
                        }
                    """)

                    if js_result:
                        nursing_found = True
                        await page.wait_for_timeout(2000)  # 等待页面跳转
                        logger.info("✅ JavaScript强制点击成功导航到月間スケージュール页面")
                    else:
                        logger.warning("⚠️ JavaScript强制点击失败")

                except Exception as e:
                    logger.warning(f"⚠️ JavaScript强制点击异常: {e}")

            # 策略3：使用智能选择器作为备用
            if not nursing_found:
                try:
                    logger.info("🎯 策略3：使用智能选择器")
                    success = await self.selector_executor.smart_click(
                        workflow="kaipoke_tennki",
                        category="navigation",
                        element="nursing_menu_hover"
                    )
                    if success:
                        # 等待下拉菜单出现
                        await page.wait_for_timeout(1500)

                        # 点击看护记录菜单项
                        success2 = await self.selector_executor.smart_click(
                            workflow="kaipoke_tennki",
                            category="navigation",
                            element="nursing_menu_click"
                        )
                        if success2:
                            nursing_found = True
                            logger.info("✅ 智能选择器成功导航到看护页面")
                except Exception as e:
                    logger.warning(f"⚠️ 智能选择器失败: {e}")

            # 策略4：查找包含"月間スケージュール"或相关文本的链接
            if not nursing_found:
                try:
                    logger.info("🎯 策略4：查找月間スケージュール相关链接")

                    # 查找所有可能的月間スケージュール相关链接
                    schedule_selectors = [
                        "text=月間スケジュール",
                        "text=月間スケージュール",
                        "a:has-text('月間')",
                        "a:has-text('スケジュール')",
                        "a:has-text('スケージュール')",
                        "text=訪問看護",
                        "text=看護記録",
                        "a:has-text('看護')",
                        "[href*='schedule']",
                        "[href*='nursing']"
                    ]

                    for selector in schedule_selectors:
                        try:
                            await page.wait_for_selector(selector, timeout=3000)
                            await page.click(selector)
                            nursing_found = True
                            logger.info(f"✅ 使用选择器 {selector} 成功导航到月間スケージュール页面")
                            break
                        except:
                            continue

                except Exception as e:
                    logger.warning(f"⚠️ 月間スケージュール链接匹配失败: {e}")

            if not nursing_found:
                # 🆕 诊断信息：列出页面上所有可用的菜单
                await self._diagnose_available_menus(page)
                raise RuntimeError("所有策略都无法找到月間スケージュール页面")

            # 🆕 等待月間スケージュール页面加载并验证
            await page.wait_for_load_state('networkidle', timeout=30000)

            # 🆕 验证是否成功进入月間スケージュール页面
            await self._verify_monthly_schedule_page(page)

            logger.info("✅ 成功导航到月間スケージュール页面，页面已加载")

        except Exception as e:
            logger.error(f"❌ 导航到月間スケージュール页面失败: {e}")
            raise RuntimeError(f"导航到月間スケージュール页面失败: {e}")

    async def _verify_monthly_schedule_page(self, page):
        """🆕 验证是否成功进入月間スケージュール页面"""
        try:
            logger.debug("🔍 验证月間スケージュール页面...")

            # 检查页面URL是否包含相关关键词
            current_url = page.url
            logger.debug(f"📄 当前页面URL: {current_url}")

            # 检查页面标题或关键元素
            page_indicators = [
                'h1:has-text("月間スケジュール")',
                'h1:has-text("月間スケージュール")',
                '.pulldownUser',  # 用户选择下拉框
                '#selectServiceOfferYm',  # 年月选择器
                'text=新規追加',  # 新增按钮
                '.form-control'  # 表单控件
            ]

            found_indicators = []
            for indicator in page_indicators:
                try:
                    if await page.wait_for_selector(indicator, timeout=3000):
                        found_indicators.append(indicator)
                except:
                    continue

            if found_indicators:
                logger.info(f"✅ 月間スケージュール页面验证成功，找到指示器: {found_indicators}")
            else:
                logger.warning("⚠️ 月間スケージュール页面验证失败，未找到预期的页面元素")

        except Exception as e:
            logger.warning(f"⚠️ 月間スケージュール页面验证异常: {e}")

    async def _diagnose_available_menus(self, page):
        """🆕 诊断页面上可用的菜单项"""
        try:
            logger.info("🔍 诊断页面上可用的菜单项...")

            available_menus = await page.evaluate("""
                () => {
                    const menus = Array.from(document.querySelectorAll('a, button, .menu-item, .dropdown-item, .dropdown-toggle'));
                    return menus
                        .filter(menu => menu.textContent.trim())
                        .map(menu => ({
                            text: menu.textContent.trim(),
                            tag: menu.tagName,
                            classes: menu.className,
                            visible: menu.offsetParent !== null,
                            href: menu.href || ''
                        }))
                        .slice(0, 20); // 显示前20个菜单项
                }
            """)

            logger.info("📋 页面上可用的菜单项:")
            for i, menu in enumerate(available_menus, 1):
                visibility = "可见" if menu['visible'] else "隐藏"
                href_info = f", href: {menu['href']}" if menu['href'] else ""
                logger.info(f"  {i}. {menu['text']} ({menu['tag']}, {visibility}{href_info})")

        except Exception as e:
            logger.warning(f"⚠️ 诊断菜单项失败: {e}")